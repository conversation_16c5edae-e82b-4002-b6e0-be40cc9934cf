import axios from 'axios';

const config = {headers: {'token': process.env.TOKEN}}
const serverUrl = process.env.SERVER_URL

export const getTestAccounts = async () => {
  const resp: any = await axios.get(`${serverUrl}/automation-test/test-account`, config)
  if (resp.data.status === 'ok') {
    return resp.data.data
  } else {
    return null
  }
}

export const getDeepLink = async (email: string) => {
  const resp: any = await axios.post(`${serverUrl}/automation-test/login-email`, {
    email,
    redirectUrl: "https://fonos.vn/finishSignin"
  }, config)
  if (resp.data.status === 'ok') {
    return resp.data.deepLink
  } else {
    return null
  }
}

export const deleteTestAccount = async (email: string) => {
  const resp: any = await axios.post(`${serverUrl}/automation-test/remove-test-account`, {
    email,
  }, config)
  if (resp.data.status === 'ok') {
    return resp.data.deepLink
  } else {
    return null
  }
}

export const getCategoryGroups = async () => {
  const resp: any = await axios.get(`${serverUrl}/category-groups`, config)
  if (resp.data) {
    return resp.data.data
  } else {
    return null
  }
}