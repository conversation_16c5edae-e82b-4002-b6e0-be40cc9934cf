import { driver } from '@wdio/globals'


export const select = (selector: string): ReturnType<WebdriverIO.Browser["$"]> => {
  let slt = `~${selector}`
  // let slt = `-ios predicate string:name == "${selector}" visible == 1 && accessible == 1`

  if (driver.isAndroid) {
    slt = `android=new UiSelector().resourceId("${selector}")`

  }

  return $(slt)
}

export const scrollDownIos = async (scrollDuration: number) => {
  const startPercentage = 30;
  const endPercentage = 80;
  const anchorPercentage = 30;
  const { width, height } = await driver.getWindowSize();
  const anchor = height * anchorPercentage / 100;
  const startPoint = width * startPercentage / 100;
  const endPoint = -(height * endPercentage / 100);


  console.log(`${startPoint}:${anchor} - ${startPoint}:${endPoint}`)

  await driver.touchPerform([
    {
      action: 'press',
      options: {
        x: 300,
        y: 700,
      },
    },
    {
      action: 'wait',
      options: {
        ms: scrollDuration,
      },
    },
    {
      action: 'moveTo',
      options: {
        x: 300,
        y: -900,
      },
    },
    {
      action: 'release',
      options: {},
    },
  ]);
}

export const scrollDown = async (scrollDuration: number) => {
  if (driver.isIOS) {
    await scrollDownIos(scrollDuration)
    return
  }

  const startPercentage = 30;
  const endPercentage = 90;
  const anchorPercentage = 30;
  const { width, height } = await driver.getWindowSize();
  const density = (await driver.getDisplayDensity()) / 100
  const anchor = height * anchorPercentage / 100;
  const startPoint = width * startPercentage / 100;
  const endPoint = -(height * endPercentage / 100);

  console.log(density)
  console.log(`${startPoint}:${anchor} - ${startPoint}:${endPoint * density}`)



  await driver.touchPerform([
    {
      action: 'press',
      options: {
        x: startPoint,
        y: anchor,
      },
    },
    {
      action: 'wait',
      options: {
        ms: scrollDuration,
      },
    },
    {
      action: 'moveTo',
      options: {
        x: startPoint,
        y: endPoint * density,
      },
    },
    {
      action: 'release',
      options: {},
    },
  ]);
}

export const scrollUp = async (scrollDuration: number) => {
  const startPercentage = 25;
  const endPercentage = 90;
  const anchorPercentage = 75;
  const { width, height } = await driver.getWindowSize();
  const density = (await driver.getDisplayDensity()) / 100
  const anchor = height * anchorPercentage / 100;
  const startPoint = width * startPercentage / 100;
  const endPoint = height * endPercentage / 100;

  console.log(`${startPoint}:${anchor} - ${startPoint}:${endPoint * density}`)

  await driver.touchPerform([
    {
      action: 'press',
      options: {
        x: startPoint,
        y: anchor,
      },
    },
    {
      action: 'wait',
      options: {
        ms: scrollDuration,
      },
    },
    {
      action: 'moveTo',
      options: {
        x: startPoint,
        y: endPoint * density,
      },
    },
    {
      action: 'release',
      options: {},
    },
  ]);
}