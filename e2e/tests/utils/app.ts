import {driver} from "@wdio/globals";

export const activateApp = async () => {
  // driver.execute('mobile: launchApp', { bundleId: 'com.apple.mobilesafari' });
  // await delay(10000)
  if (process.env.APP_ID) {
    await driver.activateApp(process.env.APP_ID);
  }
}

export const closeApp = async () => {
  await driver.closeApp()
}

export const closeApp = async () => {
  await driver.closeApp()
}

export const delay = (ms: number) => new Promise(res => setTimeout(res, ms));

export const openDeepLink = async (deepLink: string) => {
  if (!process.env.APP_ID) {
    return
  }

  if (driver.isIOS) {
    // Launch Safari to open the deep link
    // driver.execute('mobile: launchApp', { bundleId: 'com.apple.mobilesafari' });
    await driver.activateApp("com.apple.mobilesafari")
    await delay(5000)

    // Add the deep link url in Safari in the `URL`-field
    // This can be 2 different elements, or the button, or the text field
    // Use the predicate string because  the accessibility label will return 2 different types
    // of elements making it flaky to use. With predicate string we can be more precise
    // const urlButtonSelector = 'type == \'XCUIElementTypeTextField\' && name CONTAINS \'URL\'';
    const urlFieldSelector = 'type == \'XCUIElementTypeTextField\' && (name CONTAINS \'URL\' || name CONTAINS \'TabBarItemTitle\')';
    // const urlButton = $(`-ios predicate string:${ urlButtonSelector }`);
    const urlField = $(`-ios predicate string:${ urlFieldSelector }`);

    // Wait for the url button to appear and click on it so the text field will appear
    // iOS 13 now has the keyboard open by default because the URL field has focus when opening the Safari browser

    // Submit the url and add a break
    await urlField.setValue(`${ deepLink }\uE007`);

    // Wait for the notification and accept it
    const openSelector = 'type == \'XCUIElementTypeLink\' && name CONTAINS \'OPEN\'';
    const openButton = $(`-ios predicate string:${ openSelector }`);
    await openButton.waitForDisplayed({ timeout: 15000 });

    await delay(3000)
    await openButton.click();

    // driver.switchContext("NATIVE_APP")
  } else {
    await driver.execute('mobile:deepLink', {
      url: deepLink,
      package: process.env.APP_ID,
    })
  }


}