import {activateApp, delay, openDeepLink} from "../utils/app"
import WelcomeScreen from "../screenObjects/WelcomeScreen"
import LoginScreen from "../screenObjects/Authentication/LoginScreen"
import HomeScreen from "../screenObjects/Home/HomeScreen";
import BookDetailScreen from "../screenObjects/Home/BookDetailScreen";
import {deleteTestAccount, getCategoryGroups, getDeepLink} from "../utils/apis";
import LoginEmailScreen from "../screenObjects/Authentication/LoginEmailScreen";
import {scrollDown, scrollUp} from "../utils/selector";
import {emailGenerator, getRandomArbitrary} from "../utils/utils";
import CollectPersonalInfoScreen from "../screenObjects/CollectPersonalInfoScreen";
import OnboardingScreen from "../screenObjects/Home/OnboardingScreen";
import PaywallScreen from "../screenObjects/PaywallScreen";

describe("Fonos Automation Test for New User", () => {
  const welcomeScreen = new WelcomeScreen()
  const loginScreen = new LoginScreen()
  const homeScreen = new HomeScreen()
  const bookDetailScreen = new BookDetailScreen()
  const loginEmailScreen = new LoginEmailScreen()
  const collectPersonalInfoScreen = new CollectPersonalInfoScreen()
  const onboardingScreen = new OnboardingScreen()
  const paywallScreen = new PaywallScreen()
  const testEmail = emailGenerator("fonos.test.vn")

  beforeAll(async () => {
    await activateApp();
  });

  it("should login with random Email", async () => {
    await welcomeScreen.waitForIsShown(true, 20000);
    await welcomeScreen.loginButton.click()
    await loginScreen.waitForIsShown()
    await loginScreen.loginWithEmailButton.click()
    await loginEmailScreen.waitForIsShown()
    await loginEmailScreen.emailInput.setValue(testEmail)
    await delay(1000)
    await loginEmailScreen.sendEmail.click()
    await delay(2000)
    // await closeApp()
    const deepLink = await getDeepLink(testEmail)
    await openDeepLink(deepLink)
    // Await login via deeplink
    await delay(10000)
  })

  it("Onboarding Page", async () => {
    // Input profile
    await collectPersonalInfoScreen.waitForIsShown(true, 10000)
    // await collectPersonalInfoScreen.inputName.setValue("Tester Fonos")
    await collectPersonalInfoScreen.submit.click()

    // Select some category
    await onboardingScreen.waitForIsShown()
    const categoryGroup = await getCategoryGroups()
    // console.log(JSON.stringify(categoryGroup))
    console.log(categoryGroup[getRandomArbitrary(0, categoryGroup.length -1)].id)
    await onboardingScreen.selectCategory(categoryGroup[getRandomArbitrary(0, categoryGroup.length -1)].id).click()
    await onboardingScreen.selectCategory(categoryGroup[getRandomArbitrary(0, categoryGroup.length - 1)].id).click()
    await onboardingScreen.selectCategory(categoryGroup[getRandomArbitrary(0, categoryGroup.length - 1)].id).click()
    await onboardingScreen.next.click()

    await onboardingScreen.skipNotification.click()
    await delay(2000)
    await onboardingScreen.next.click()
  })

  it("New Home Page", async () => {
    await homeScreen.waitForIsShown(true, 40000)

    // Close paywall
    await delay(3000)
    await paywallScreen.waitForIsShown()
    await paywallScreen.closeDialog.click()

    await delay(2000)

    await scrollDown(30000)
    await scrollDown(20000)

    await scrollUp(15000)
    await scrollUp(15000)
  })

  it("Open Book Detail Page", async () => {
    await homeScreen.openBookDetailPageFromCarouse.click()
    await bookDetailScreen.waitForIsShown(true, 10000)
    await delay(2000)
  })

  it("Open my lib", async () => {
    homeScreen.openTabMyLib.click()
    await delay(5000)
  })

  it("Open tab new and hot", async () => {
    homeScreen.openTabNewAndHot.click()
    await delay(5000)
  })

  it("Open Upgrade Tab", async () => {
    await homeScreen.openTabUpgrade.click()
    await delay(3000)
  })

  it("Remove test account", async () => {
    await deleteTestAccount(testEmail)
    await delay(1000)
  })
})