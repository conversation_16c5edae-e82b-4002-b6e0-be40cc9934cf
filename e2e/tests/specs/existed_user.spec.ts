import {activateApp, delay, openDeepLink} from "../utils/app"
import WelcomeScreen from "../screenObjects/WelcomeScreen"
import LoginScreen from "../screenObjects/Authentication/LoginScreen"
import HomeScreen from "../screenObjects/Home/HomeScreen";
import BookDetailScreen from "../screenObjects/Home/BookDetailScreen";
import {getDeepLink, getTestAccounts} from "../utils/apis";
import LoginEmailScreen from "../screenObjects/Authentication/LoginEmailScreen"
import {scrollDown, scrollUp} from "../utils/selector";

describe("Fonos Automation Test for Existed User", () => {
  const welcomeScreen = new WelcomeScreen()
  const loginScreen = new LoginScreen()
  const homeScreen = new HomeScreen()
  const bookDetailScreen = new BookDetailScreen()
  const loginEmailScreen = new LoginEmailScreen()

  beforeAll(async () => {
    await activateApp();
  });

  beforeEach(async () => {

  })

  it('test', async () => {
    await scrollDown(2000)
    await scrollDown(2000)


    // await scrollDown(30000)
    // await scrollDown(20000)

    // await scrollUp(15000)
    // await scrollUp(15000)
  })

  // it("should login with Email", async () => {
  //   await welcomeScreen.waitForIsShown(true, 20000);
  //   const testAccounts = await getTestAccounts()
  //   let user = null
  //   if (testAccounts && testAccounts.length > 0) {
  //     const randomNumber = Math.floor(Math.random() * (testAccounts.length - 1));
  //     user = testAccounts[randomNumber]
  //     console.log(`Test user: ${JSON.stringify(user)}`)
  //   }
  //   await welcomeScreen.loginButton.click()
  //   await loginScreen.waitForIsShown()
  //   await loginScreen.loginWithEmailButton.click()
  //   await loginEmailScreen.waitForIsShown()
  //   await loginEmailScreen.emailInput.setValue(user.email)
  //   await delay(1000)
  //   await loginEmailScreen.sendEmail.click()
  //   await delay(2000)
  //   // await closeApp()
  //   const deepLink = await getDeepLink(user.email)
  //   await openDeepLink(deepLink)
  //   // Await login via deeplink
  //   await delay(10000)
  // })
  //
  // it("New Home Page", async () => {
  //   await homeScreen.waitForIsShown(true, 40000)
  //
  //   await scrollDown(30000)
  //   await scrollDown(20000)
  //
  //   await scrollUp(15000)
  //   await scrollUp(15000)
  // })
  //
  // it("Open Book Detail Page", async () => {
  //   await homeScreen.openBookDetailPageFromCarouse.click()
  //   await bookDetailScreen.waitForIsShown(true, 10000)
  //   await delay(2000)
  // })
  //
  // it("Open my lib", async () => {
  //   homeScreen.openTabMyLib.click()
  //   await delay(5000)
  // })
  //
  // it("Open tab new and hot", async () => {
  //   homeScreen.openTabNewAndHot.click()
  //   await delay(5000)
  // })
  //
  // it("Open Upgrade Tab", async () => {
  //   await homeScreen.openTabUpgrade.click()
  //   await delay(3000)
  // })
})