import AppScreen from "../AppScreen";
import {select} from "../../utils/selector";

class OnboardingScreen extends AppScreen {
  constructor () {
    super(OnboardingScreen.locators.btnContinue);
  }

  public static locators = {
    btnItemCategory: "btn-item-category",
    btnContinue: "btn-continue",
    btnSkipNotifications: "btn-skip-notifications"
  }

  public selectCategory(catId: number): ReturnType<WebdriverIO.Browser["$"]> {
    return select(`${OnboardingScreen.locators.btnItemCategory}_${catId}`)
  }

  get next(): ReturnType<WebdriverIO.Browser["$"]> {
    return select(OnboardingScreen.locators.btnContinue)
  }

  get skipNotification(): ReturnType<WebdriverIO.Browser["$"]> {
    return select(OnboardingScreen.locators.btnSkipNotifications)
  }
}

export default OnboardingScreen