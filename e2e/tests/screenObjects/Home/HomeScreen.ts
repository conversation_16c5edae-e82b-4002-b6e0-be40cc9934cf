import AppScreen from '../AppScreen'
import { select } from '../../utils/selector'

class HomeScreen extends AppScreen {
  constructor() {
    super(HomeScreen.locators.carouselMain)
  }

  public static locators = {
    carouselMain: '__CAROUSEL_ITEM_0_READY__',
    carouselLast: '__CAROUSEL_ITEM_1_READY__',
    carouselFirst: '__CAROUSEL_ITEM_9_READY__',
    searchButton: 'home-search',
    myAccountButton: 'home-my-account',
    tabHome: 'home-tab-homepage',
    tabNewAndHot: 'home-tab-new-and-hot',
    tabDeal: 'home-tab-deal',
    tabLibrary: 'home-tab-library',
    tabChallenges: 'home-tab-challenges',
    tabUpgrade: 'home-tab-upgrade',
    menuAudioBook: 'home-menu-audio-book',
    menuPodcast: 'home-menu-podcast',
    menuMeditation: 'home-menu-meditation',
    menuAll: 'home-menu-all',
    menuEbook: 'home-menu-ebook',
    menuBookSummary: 'home-menu-book-summary',
    menuSleepStory: 'home-menu-sleep-story',
    menuKidsStories: 'home-menu-kids-stories',
    menuMusic: 'home-menu-music',
    menuEnglishBook: 'home-menu-english-book',
    menuPodCourse: 'home-menu-pod-course',
    recommendationCategories: 'recommendation-categories',
  }

  get openBookDetailPageFromCarouse(): ReturnType<WebdriverIO.Browser['$']> {
    return select(HomeScreen.locators.carouselMain)
  }

  get openTabMyLib(): ReturnType<WebdriverIO.Browser['$']> {
    return select(HomeScreen.locators.tabLibrary)
  }

  get openTabNewAndHot(): ReturnType<WebdriverIO.Browser['$']> {
    return select(HomeScreen.locators.tabNewAndHot)
  }

  get openTabUpgrade(): ReturnType<WebdriverIO.Browser['$']> {
    return select(HomeScreen.locators.tabUpgrade)
  }

  openMenuAudioBook() {
    return select(HomeScreen.locators.menuAudioBook).click()
  }
}

export default HomeScreen
