import AppScreen from "../../AppScreen";
import { select } from "../../../utils/selector";

class PodcastHomeScreen extends AppScreen {
  constructor () {
    super(PodcastHomeScreen.locators.carouselMain);
  }

  public static locators = {
    carouselMain: "__CAROUSEL_ITEM_0_READY__",
    carouselLast: "__CAROUSEL_ITEM_1_READY__",
    carouselFirst: "__CAROUSEL_ITEM_9_READY__",
  }

  get openChannelDetailPageFromCarousel(): ReturnType<WebdriverIO.Browser["$"]> {
    return select(PodcastHomeScreen.locators.carouselMain);
  }

}

export default PodcastHomeScreen