import AppScreen from "./AppScreen";
import { select } from "../utils/selector";

class WelcomeScreen extends AppScreen {
  constructor () {
    super(WelcomeScreen.locators.startButton);
  }

  public static locators = {
    startButton: "welcome-btn-start",
    loginButton: "welcome-btn-login",
  }

  get loginButton (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(WelcomeScreen.locators.loginButton);
  }
}

export default WelcomeScreen