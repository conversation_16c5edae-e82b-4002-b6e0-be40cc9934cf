import AppScreen from "./AppScreen";
import {select} from "../utils/selector";

class CollectPersonalInfoScreen extends AppScreen {
  constructor () {
    super(CollectPersonalInfoScreen.locators.nameTextInput);
  }

  public static locators = {
    nameTextInput: "name-text-input",
    btnSubmit: "btn-submit"
  }

  get inputName(): ReturnType<WebdriverIO.Browser["$"]> {
    return select(CollectPersonalInfoScreen.locators.nameTextInput);
  }

  get submit(): ReturnType<WebdriverIO.Browser["$"]> {
    return select(CollectPersonalInfoScreen.locators.btnSubmit);
  }
}

export default CollectPersonalInfoScreen