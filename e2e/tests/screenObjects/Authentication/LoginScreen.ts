import AppScreen from "../AppScreen";
import { select } from "../../utils/selector";

class LoginScreen extends AppScreen {
  constructor () {
    super(LoginScreen.locators.loginMethodPhone);
  }

  public static locators = {
    loginMethodPhone: "login-method-Phone",
    loginMethodEmail: "login-method-Email",
    loginMethodGoogle: "login-method-Google",
    loginMethodFacebook: "login-method-Facebook",
    loginMethodApple: "login-method-Apple",
    signupButton: "signup-btn",
  }

  get loginWithPhoneNumberButton (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginScreen.locators.loginMethodPhone);
  }

  get loginWithEmailButton (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginScreen.locators.loginMethodEmail);
  }

  get loginWithGoogleButton (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginScreen.locators.loginMethodGoogle);
  }

  get loginWithFacebookButton (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginScreen.locators.loginMethodFacebook);
  }
}

export default LoginScreen