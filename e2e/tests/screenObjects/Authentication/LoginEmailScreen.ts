import AppScreen from "../AppScreen";
import {select} from "../../utils/selector";

class LoginEmailScreen extends AppScreen {
  constructor () {
    super(LoginEmailScreen.locators.emailInput);
  }

  public static locators = {
    emailInput: "login-email-input",
    sendEmailBtn: "login-email-submit",
  }

  get emailInput (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginEmailScreen.locators.emailInput)
  }

  get sendEmail (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginEmailScreen.locators.sendEmailBtn)
  }
}

export default LoginEmailScreen