import AppScreen from "../AppScreen";
import { select } from "../../utils/selector";

class LoginPhoneScreen extends AppScreen {
  constructor () {
    super(LoginPhoneScreen.locators.phoneNumberInput);
  }

  public static locators = {
    phoneNumberInput: "login-phone-input",
    otpInput: "login-phone-otp-input",
    nextBtn: "login-phone-submit",
  }

  get phoneNumberInput (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginPhoneScreen.locators.phoneNumberInput)
  }

  get otpInput (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginPhoneScreen.locators.otpInput)
  }

  get nextBtn (): ReturnType<WebdriverIO.Browser["$"]> {
    return select(LoginPhoneScreen.locators.nextBtn)
  }

  async inputPhoneNumber (phoneNumber: string) {
    await this.phoneNumberInput.setValue(phoneNumber)
  }

  async inputOtp(otp: string) {
    // await this.otpInput.waitForDisplayed()
    await this.otpInput.setValue(otp)
  }
}

export default LoginPhoneScreen