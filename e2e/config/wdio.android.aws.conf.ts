import config  from './aws.conf';

config.capabilities = [
  {
    // @ts-ignore
    platformName: 'Android',
    // @ts-ignore
    maxInstances: 1,
    'appium:options': {
      // @ts-ignore
      deviceName: process.env.DEVICEFARM_DEVICE_NAME,
      automationName: 'uiautomator2',
      app: process.env.DEVICEFARM_APP_PATH,
      appWaitActivity: '*',
      appWaitDuration: 100000,
      uiautomator2ServerLaunchTimeout: 200000,
      uiautomator2ServerInstallTimeout: 200000,
      appWaitForLaunch: true,
      useNewWDA: true,
      usePrebuiltWDA: false,
      autoGrantPermissions: true,
      adbExecTimeout: 200000,
      androidInstallTimeout: 150000,
      ignoreHiddenApiPolicyError: true
    },
  },
];

exports.config = config;