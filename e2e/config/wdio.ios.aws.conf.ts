import config from './aws.conf';

config.capabilities = [
  {
    // @ts-ignore
    platformName: 'iOS',
    // @ts-ignore
    maxInstances: 1,
    'appium:options': {
      // @ts-ignore
      udid: process.env.DEVICEFARM_DEVICE_UDID_FOR_APPIUM,
      automationName: "XCUITest",
      app: process.env.DEVICEFARM_APP_PATH,
      derivedDataPath: process.env.DEVICEFARM_WDA_DERIVED_DATA_PATH,
      enforceAppInstall: false,
      appWaitForLaunch: true,
      appWaitDuration: 100000,
      autoGrantPermissions: true,
      useNewWDA: false,
      usePrebuiltWDA: true,
      wdaConnectionTimeout: 180000,
      autoAcceptAlerts: true,
    },
  },
];

console.log('config: ', JSON.stringify(config, null, 1))

exports.config = config;