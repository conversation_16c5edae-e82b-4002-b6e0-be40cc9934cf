import config from './appium.conf';

config.capabilities = [
  {
    // @ts-ignore
    platformName: 'iOS',
    // @ts-ignore
    maxInstances: 1,
    'appium:options': {
      // @ts-ignore
      "automationName": "XCUITest",
      "deviceName": process.env.DEVICE,
      "platformVersion": process.env.OS_VERSION,
      "bundleId": process.env.APP_ID,
      "webviewConnectTimeout": 9000,
      useNewWDA: true,
      usePrebuiltWDA: false,
      wdaConnectionTimeout: 180000,
      appWaitForLaunch: true,
      noReset: false,
      fullReset: false,
      autoAcceptAlerts: true,
    },
  },
];

exports.config = config;
