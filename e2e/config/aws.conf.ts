import { config } from "./wdio.shared.conf";
import axios from 'axios';

config.services = (config.services ? config.services : []).concat([
  [
    "appium",
    {
      // This will use the globally installed version of Appium
      command: "appium",
      args: {
        address: 'localhost',
        port: 4723,
        // Write the Appium logs to a file in the root of the directory
        log: process.env.DEVICEFARM_LOG_DIR + "/appium.log",
      },
    },
  ],
]);

config.port = 4723;

config.logLevel = 'debug';

/**
 * Gets executed after all workers have shut down and the process is about to exit.
 * An error thrown in the `onComplete` hook will result in the test run failing.
 * @param {object} exitCode 0 - success, 1 - fail
 * @param {object} config wdio configuration object
 * @param {Array.<Object>} capabilities list of capabilities details
 * @param {<Object>} results object containing test results
 */
const failTests: { name: string; error: any; }[] = []

config.afterTest = function (test, context, { error, passed }) {
  if (!passed) {
    failTests.push({
      name: test.title,
      error: error.stack
    })
  }
}

config.onComplete = function (exitCode) {
  // Send slack notification about test results
  const slackUrl = '*******************************************************************************'
  let slackMessage = {
    text: `[Device Farm] *${process.env.DEVICEFARM_DEVICE_PLATFORM_NAME} - ${process.env.DEVICEFARM_DEVICE_NAME}* test results`,
    mrkdwn: true,
    attachments: undefined
  }
  if (exitCode === 0) {
    slackMessage.text += ' passed! :tada:'
  } else {
    slackMessage.text += " failed! :cry:"
    if (failTests.length) {
      // @ts-ignore
      slackMessage.attachments = [{
        color: 'danger',
        fields: failTests.map(test => ({
          title: test.name,
          value: test.error,
          short: false
        }))
      }]
    }
  }

  axios.post(slackUrl, slackMessage)
}

export default config;