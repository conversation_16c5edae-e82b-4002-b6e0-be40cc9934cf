import { config } from "./wdio.shared.conf";
//
// ======
// Appium
// ======
//
config.services = (config.services ? config.services : []).concat([
  [
    "appium",
    {
      // This will use the globally installed version of Appium
      command: "appium",
      args: {
        address: 'localhost',
        port: 4723,
        // Write the Appium logs to a file in the root of the directory
        log: "./appium.log",
      },
    },
  ],
]);

config.port = 4723;

if (process.env.CIRCLECI) {
  config.reporters = [
    "dot",
    [
      "junit",
      {
        outputDir: "./test-results",
        outputFileFormat: function (options) {
          return `results-${options.cid}.xml`;
        }
      }
    ]
  ];
}

export default config;