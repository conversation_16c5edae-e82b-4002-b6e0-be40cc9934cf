import config  from './appium.conf';

config.capabilities = [
    {
      // @ts-ignore
      platformName: 'Android',
      // @ts-ignore
      maxInstances: 1,
      'appium:options': {
          // @ts-ignore
          // deviceName: process.env.DEVICE,
          automationName: 'uiautomator2',
          appPackage: process.env.APP_ID,
          appWaitPackage: process.env.APP_ID,
          appActivity: `${process.env.MAIN_ACTIVITY}`,
          appWaitActivity: `${process.env.MAIN_ACTIVITY}`,
          uiautomator2ServerLaunchTimeout: 200000,
          uiautomator2ServerInstallTimeout: 200000,
          appWaitForLaunch: true,
          autoGrantPermissions: true,
          adbExecTimeout: 200000,
          androidInstallTimeout: 150000,
          ignoreHiddenApiPolicyError: true,
          noReset: true,
          fullReset: false
        },
    },
];

exports.config = config;