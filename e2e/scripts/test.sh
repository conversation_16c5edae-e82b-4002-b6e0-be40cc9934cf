APP_ENV="dev"
APP_OS="ios"
DEVICE="iPhone 14 Pro Max"
OS_VERSION="16.4"
MAIN_ACTIVITY="vn.fonos.mobile.MainActivity"
TOKEN="N0ZMGBxpm7h3qWZrXtQJFoFfwB8"
SERVER_URL="https://api-dev.fonos.dev"

while getopts ":e:o:d:v:m:t:s:" opt; do
  case $opt in
    e) APP_ENV="$OPTARG"
    ;;
    o) APP_OS="$OPTARG"
    ;;
    d) DEVICE="$OPTARG"
    ;;
    v) OS_VERSION="$OPTARG"
    ;;
    t) TOKEN="$OPTARG"
    ;;
    s) SERVER_URL="$OPTARG"
    ;;
    \?) echo "${RED}Invalid option -$OPTARG${NO_COLOR}" >&2
    ;;
  esac
done

echo -e "${BLUE}* * * * *"
echo -e "👷  E2E test app"
echo -e "* * * * *${NO_COLOR}"

if [[ $APP_ENV == "dev" ]]; then
  APP_ID="vn.fonos.mobile.dev"
fi
if [[ $APP_ENV == "staging" ]]; then
  APP_ID="vn.fonos.mobile.staging"
fi
if [[ $APP_ENV == "sandbox" ]]; then
  APP_ID="vn.fonos.mobile.staging"
fi
if [[ $APP_ENV == "production" ]]; then
  APP_ID="vn.fonos.mobile"
fi

if [[ $APP_OS != "android" ]]; then
  echo -e "${GREEN}- - - - -"
  echo -e "🍎 iOS $APP_ENV"
  echo -e "- - - - -${NO_COLOR}"
  APP_ID=${APP_ID} DEVICE=${DEVICE} OS_VERSION=${OS_VERSION} MAIN_ACTIVITY=${MAIN_ACTIVITY} TOKEN=${TOKEN} SERVER_URL=${SERVER_URL} wdio config/wdio.ios.local.conf.ts
fi
if [[ $APP_OS != "ios" ]]; then
  echo -e "${YELLOW}- - - - -"
  echo "🤖 Android $APP_ENV"
  echo -e "- - - - -${NO_COLOR}"
  APP_ID=${APP_ID} MAIN_ACTIVITY=${MAIN_ACTIVITY} TOKEN=${TOKEN} SERVER_URL=${SERVER_URL} wdio config/wdio.android.local.conf.ts
fi