version: 0.1

# Phases are collection of commands that get executed on Device Farm.
phases:
  # The installation phase includes commands that install dependencies that your tests use.
  # Default dependencies for testing frameworks supported on Device Farm are already installed.
  install:
    commands:
      - nvm install 16.20.0
      - export APPIUM_VERSION=2.1.3
      - npm install -g appium@2.1.3 yarn
      - appium driver install uiautomator2
      - echo "Navigate to test package directory"
      - cd $DEVICEFARM_TEST_PACKAGE_PATH
      - yarn install

  # The pre-test phase includes commands that set up your test environment.
  pre_test:
    commands:
    # The test phase includes commands that start your test suite execution.
  test:
    commands:
      # Change the directory to node_modules folder as it has your test code and the dependency node modules.
      - cd $DEVICEFARM_TEST_PACKAGE_PATH
      - echo "Start Appium Node test"
      - yarn device-farm-android

  # The post test phase includes commands that are run after your tests are executed.
  post_test:
    commands:

# The artifacts phase lets you specify the location where your tests logs, device logs will be stored.
# And also let you specify the location of your test logs and artifacts which you want to be collected by Device Farm.
# These logs and artifacts will be available through ListArtifacts API in Device Farm.
artifacts:
  # By default, Device Farm will collect your artifacts from following directories
  - $DEVICEFARM_LOG_DIR