{
  "compilerOptions": {
    "outDir": "./.tsbuild/",
    "sourceMap": false,
    "target": "es2019",
    "module": "commonjs",
    "removeComments": true,
    "noImplicitAny": true,
    "strictPropertyInitialization": true,
    "strictNullChecks": true,
    "types": [
      "node",
      "webdriverio/async",
      "@wdio/jasmine-framework",
      "expect-webdriverio",
      "@wdio/sauce-service",
    ]
  },
  "include": [
    "./config/**/*.ts",
    "./tests/**/*.ts",
    "node_modules/@types/jasmine/index.d.ts",
    "node_modules/@wdio/globals/types.d.ts"
  ]
}