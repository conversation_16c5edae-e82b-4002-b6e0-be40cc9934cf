{"name": "e2e", "packageManager": "yarn@3.6.4", "devDependencies": {"@types/jasmine": "^4.3.5", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.32.0", "@wdio/appium-service": "^8.16.6", "@wdio/cli": "^8.16.6", "@wdio/dot-reporter": "^8.16.7", "@wdio/jasmine-framework": "^8.16.6", "@wdio/junit-reporter": "^8.16.7", "@wdio/local-runner": "^8.16.6", "@wdio/spec-reporter": "^8.16.3", "eslint": "^8.21.0", "eslint-plugin-wdio": "^8.8.7", "ts-node": "^10.9.1", "typescript": "^5.0.4", "wdio-wait-for": "^3.0.6", "webdriverio": "^8.16.6"}, "scripts": {"test": "./scripts/test.sh", "lint": "eslint config tests", "device-farm-android": "wdio ./config/wdio.android.aws.conf.ts", "device-farm-ios": "wdio ./config/wdio.ios.aws.conf.ts", "pack-zip": "npx npm-pack-zip"}, "files": ["package.json", "yarn.lock", "config", "scripts", "tests", ".eslintrc.js", "babel.config.js", "tsconfig.json"], "dependencies": {"axios": "^1.5.1"}}