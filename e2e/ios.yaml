version: 0.1

# Phases are collection of commands that get executed on Device Farm.
phases:
  # The installation phase includes commands that install dependencies that your tests use.
  # Default dependencies for testing frameworks supported on Device Farm are already installed.
  install:
    commands:
      - nvm install 16.20.0
      - export APPIUM_VERSION=2.1.3
      - npm install -g appium@2.1.3 yarn
      - appium driver install xcuitest
      # For users of Appium versions 1.15.0 and higher, your Appium version requires that the UDID of the device not contain any "-" characters
      # So, we will create a new environment variable of the UDID specifically for Appium based on your Appium version
      - >-
        export DEVICEFARM_DEVICE_UDID_FOR_APPIUM=$(echo $DEVICEFARM_DEVICE_UDID | tr -d "-");
        export EVICEFARM_WDA_DERIVED_DATA_PATH=$DEVICEFARM_WDA_DERIVED_DATA_PATH_V7;
        

      - echo "Navigate to test package directory"
      - cd $DEVICEFARM_TEST_PACKAGE_PATH
      - yarn install

  # The pre-test phase includes commands that set up your test environment.
  pre_test:
    commands:
    # The test phase includes commands that start your test suite execution.
  test:
    commands:
      # Change the directory to node_modules folder as it has your test code and the dependency node modules.
      - cd $DEVICEFARM_TEST_PACKAGE_PATH
      - echo "Start Appium Node test"
      - yarn device-farm-ios

  # The post test phase includes commands that are run after your tests are executed.
  post_test:
    commands:

# The artifacts phase lets you specify the location where your tests logs, device logs will be stored.
# And also let you specify the location of your test logs and artifacts which you want to be collected by Device Farm.
# These logs and artifacts will be available through ListArtifacts API in Device Farm.
artifacts:
  # By default, Device Farm will collect your artifacts from following directories
  - $DEVICEFARM_LOG_DIR