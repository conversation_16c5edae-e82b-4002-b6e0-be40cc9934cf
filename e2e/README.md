# Project E2E - Getting Started

Follow these steps to get started with the 'E2E' project:

## Installation

1. Install Appium (current version 2.1.3) globally via npm:
   ```bash
   > npm install -g appium@2.1.3
   > appium driver install xcuitest
   > appium driver install uiautomator2
   ```

## Prepare Device/Simulator

1. Launch your Android or iOS simulator.
2. Install the application on the simulator.

## Setup Tests

1. **Screen Objects (`tests/screenobjects`)**: Define screens with their elements and locators.

2. **Specs (`tests/specs`)**: Contains test files leveraged by Jasmine.

#### To perform E2E testing:
- Execute tests with `yarn test -o <ios|android> -e <dev|staging>`.


## Run test on AWS Device Farm

1. cd to root folder `cd ../`
2. Run command `yarn run-device-farm -o <ios|android> -e <dev|staging>`