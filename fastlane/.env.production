### GLOBAL ###
ENV='production'
DEPLOYMENT_PLATFORM='appstore'

### IOS ###
IOS_PROJECT_PATH='ios'
IOS_APP_NAME='Fonos'
IOS_APPCENTER_APP_ID='fonos-ios-production'
IOS_PROJECT_NAME='FonosMobile'
IOS_APP_ID='vn.fonos.mobile'
IOS_TEAM_ID='XWWT565B3T'
IOS_USER_ID='<EMAIL>'
IOS_ITC_TEAM_NAME='Fonos Corporation'
IOS_PLIST_PATH='FonosMobile/Info.plist'
IOS_APPSTORECONNECT_USER_ID='<EMAIL>'
GOOGLE_PLIST_FILE='GoogleService-Info-production.plist'
IOS_SCHEME='FonosMobileProduction'
IOS_CONFIGURATION='ReleaseProduction'

### IOS MATCH ###
MATCH_GIT_URL='*****************:fonos/fonos-mobile-match.git'
MATCH_GIT_BRANCH='master'
MATCH_TYPE='appstore'


### IOS GYM ###
GYM_SCHEME='FonosMobile'
GYM_OUTPUT_DIRECTORY='dist'
GYM_OUTPUT_NAME='app'

### IOS HOCKEY APP AND APP CENTER ###
IOS_IPA_PATH='dist/app.ipa'
APPCENTER_USERNAME='Fonos'

### IOS FIREBASE ###
IOS_FIREBASE_APP_ID=1:************:ios:f57400e8b9b6df37d406cf
ANDROID_FIREBASE_APP_ID=1:************:android:259946d6da34f41bd406cf
FIREBASE_SERVICE_ACCOUNT_FILE='node/credentials/firebase_admin_production.json'

### ANDROID PROJECT ###
ANDROID_PROJECT_DIR='android'
ANDROID_APK_PATH='android/app/build/outputs/apk/release/production/app-production-release.apk'
ANDROID_AAB_PATH='android/app/build/outputs/bundle/release/app-release.aab'
GRADLE_APP_IDENTIFIER='vn.fonos.mobile'
GRADLE_APP_NAME='Fonos'
ANDROID_APPCENTER_APP_ID='fonos-android-production'
ANDROID_PLAYSTORE_JSON_KEY_PATH='android/app/fastlane-json-key.json'
GOOGLE_SERVICES_FILE='google-services-production.json'
GRADLE_KEYSTORE_PATH='app/keystore.properties'
GRADLE_KEYSTORE_FILE_PATH='android/app/fonosmobile.production.keystore'
ANDROID_FLAVOR="production"

### CODEPUSH ###
IOS_CODEPUSH_DEPLOYMENT_NAME='Production'
ANDROID_CODEPUSH_DEPLOYMENT_NAME='Production'

### SECRETS ###
APP_ID=vn.fonos.mobile
PACKAGE_NAME=vn.fonos.mobile
IOS_APP_IDENTITY=**********

FIREBASE_DYNAMIC_LINK=fonos.page.link
FIREBASE_APP_DOMAIN=fonos-audio.firebaseapp.com
FIREBASE_REVERSED_CLIENT_ID=com.googleusercontent.apps.************-ovgsgppbbl7bod64fl7lrhokfsq3afqs
FIREBASE_API_KEY="AIzaSyANFxGiLrDof8sj-1r7OTjKMsS47iOEZCI"
FIREBASE_PROJECT_ID="fonos-audio"
FIRESTORE_DATABASE="asia-southeast-1"

FL_APPCENTER_API_TOKEN='4a88bc886dcf1b2f8bf4e3cc705bacb039b7f74d'

IOS_APPCENTER_APP_SECRET=''
ANDROID_APPCENTER_APP_SECRET=''

IOS_CODEPUSH_DEPLOYMENT_KEY='ysZ48VSJf2V1GN8rFVTxdrmzE6xUPQv1rxT-G'
ANDROID_CODEPUSH_DEPLOYMENT_KEY='wse2061YBHVXLFQrxJwA1cKAP9Za9C6oJ_EV5'

FB_URL_SCHEME=fb537258767081308
FB_APP_ID=537258767081308
FB_CLIENT_TOKEN=e03d95667d58e69ba68cec0b35e267d4
FB_APP_NAME='Fonos'

INSTABUG_API_KEY=57dfd5b65a5e57107ef624a001f9305f

SERVER_URL=https://production.fonos.dev
IMAGE_URL=https://fonos.fonos.dev

SUB_LITE_MONTHLY_ID='production.subscription.monthly.lite'
SUB_LITE_ANNUALLY_ID='production.subscription.annually.lite'
SUB_NEW_MONTHLY_ID='production.subscription.monthly.premium'
ONE_CREDIT_ID='production.bundle.1.credit'

SUB_MONTHLY_ID='vn.fonos.mobile.subscription.monthly'
SUB_ANNUALLY_ID='vn.fonos.mobile.subscription.annually'
SUB_TRIAL_ANNUALLY_ID='vn.fonos.mobile.subscription.trial.annually'
SUB_MONTHLY_PRO_ID='vn.fonos.mobile.sub.monthly.pro'
SUB_ANNUALLY_PRO_ID='vn.fonos.mobile.sub.annually.pro'
MORE_3_CREDIT_ID='vn.fonos.mobile.more.3.credit'
MORE_5_CREDIT_ID='vn.fonos.mobile.more.5.credit'
MORE_10_CREDIT_ID='vn.fonos.mobile.more.10.credit'
BUNDLE_3_CREDIT_ID='vn.fonos.mobile.bundle.3.credit'
BUNDLE_5_CREDIT_ID='vn.fonos.mobile.bundle.5.credit'
BUNDLE_10_CREDIT_ID='vn.fonos.mobile.bundle.10.credit'
MORE_3_CREDIT_DISCOUNT_30_ID='vn.fonos.mobile.more.3.credit.discount.30'
SUB_ANNUALLY_DISCOUNT_30_ID='vn.fonos.mobile.subscription.annually.discount.30'
SUB_ANNUALLY_PRO_DISCOUNT_20_ID='vn.fonos.mobile.sub.year.pro.20'
SUB_ANNUALLY_FREE_3_CREDITS_ID='fonos.sub.year.free.3.credit'

CHURN_SUB_QUARTERLY_DISCOUNT_50_ID='fonos.churn.sub.quarter.promo.50'
CHURN_SUB_ANNUALLY_DISCOUNT_30_ID='fonos.churn.sub.year.promo.30'

APP_SCHEME=fonos
BRANDED_DOMAIN=go.fonos.app
APPS_FLYER_DOMAIN=fonos.onelink.me
APPS_FLYER_KEY=uNqijbzRBoUr628MVEe3j5
APPS_FLYER_ONE_LINK_ID=/5RLu

REVENUECAT_PUBIC_KEY=tuXsIwpNBOcxszWdAvPspaVHYQiAxLRj

SEO_PAGE_URL=https://fonos.vn
SENTRY_DSN=https://<EMAIL>/5730578
SENTRY_AUTH_TOKEN='****************************************************************'

MIXPANEL_PROJECT_TOKEN=1190a9a90afd89ce8b55391f0d88581f

REALM_CREDENTIALS=Vvn9XXi3TvZjpKh652duP2ypyU9AXQpGYKnEekibHxbYNZ29adGcQMEZUwvyJQJq
REALM_ID=fonos-app-production-ypivm

FLEXIBLE_SYNC_REALM_CREDENTIALS=Adw2qr5tjE4dgdSDmZBdRDqGuHehcGoMns2mTK0KzjiNsVJt6ePWkRLAEGrwSALN
FLEXIBLE_SYNC_REALM_ID=fonos-flexible-sync-production-wftae

SKYEPUB_LICENSE_IOS=cc85-5cff-d28f-4de3
SKYEPUB_LICENSE_ANDROID=6dbf-9e37-84fc-c2b6

AKAMAI_DD_URL=https://fonos-dd-production.akamaized.net

ZENDESK_URL=https://zendesk-api-gateway.fonos.dev
ZENDESK_APP_ID=cb1c73359d43be9064fa68f7f31bb37174d216171bb20e2a
ZENDESK_CLIENT_ID=mobile_sdk_client_cbbc12e7df0255f29195
ZENDESK_CHAT_APP_ID=386001486694285313
ZENDESK_CHAT_ACCOUNT_KEY=cQmQi2Mkgk3NM5xXv1mqbjM1WvvyWQo2
ZENDESK_API_TOKEN=DvPNjbDmffSALUsC9shUzlsd5KoqGIcExbee5S59
ZENDESK_AGENT_USERNAME=<EMAIL>
ZENDESK_API_URL=https://zendesk-api-gateway.fonos.dev

REALM_WEB_APP_ID=fonos-realm-web-app-trapt
REALM_WEB_API_KEY=8UDddXZkXitxMEfEm9hGjVHoPWOAGgfRMGxYh8mhsr2E7dbc4uWcNYX0LjPxm4uJ
REALM_WEB_APP_HOSTNAME=https://ap-southeast-1.aws.realm.mongodb.com
GRAPHQL_URL=https://ap-southeast-1.aws.realm.mongodb.com/api/client/v2.0/app/fonos-realm-web-app-trapt/graphql

WEBSITE_URL=https://fonos.vn

CLEVERTAP_ACCOUNT_ID=445-488-556Z
CLEVERTAP_TOKEN=bb4-544
CLEVERTAP_REGION=sg1

CLOUD_CDN_URL=https://cdn.fonos.dev

ONESIGNAL_APP_ID=************************************

REALM_DISABLE_ANALYTICS=true

GOOGLE_SIGN_IN_WEB_CLIENT_ID=************-7aruptm85t4n82eekm9j2hfa98gp8k5v.apps.googleusercontent.com

SENTRY_AUTH_TOKEN=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