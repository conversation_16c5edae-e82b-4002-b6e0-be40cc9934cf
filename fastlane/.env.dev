### GLOBAL ###
ENV='dev'
DEPLOYMENT_PLATFORM='appcenter'

### IOS ###
IOS_PROJECT_PATH='ios'
IOS_APP_NAME='Fonos (dev)'
IOS_APPCENTER_APP_ID='fonos-ios-development'
IOS_PROJECT_NAME='FonosMobile'
IOS_APP_ID='vn.fonos.mobile.dev'
IOS_TEAM_ID='XWWT565B3T'
IOS_USER_ID='<EMAIL>'
IOS_ITC_TEAM_NAME='Fonos Corporation'
IOS_PLIST_PATH='FonosMobile/Info.plist'
IOS_APPSTORECONNECT_USER_ID='<EMAIL>'
GOOGLE_PLIST_FILE='GoogleService-Info-dev.plist'
IOS_SCHEME='FonosMobile'
IOS_CONFIGURATION='Release'

### IOS MATCH ###
MATCH_GIT_URL='*****************:fonos/fonos-mobile-match.git'
MATCH_GIT_BRANCH='master'
MATCH_TYPE='development'


### IOS GYM ###
GYM_SCHEME='FonosMobile'
GYM_OUTPUT_DIRECTORY='dist'
GYM_OUTPUT_NAME='app'

### IOS HOCKEY APP AND APP CENTER ###
IOS_IPA_PATH='dist/app.ipa'
APPCENTER_USERNAME='Fonos'

### IOS FIREBASE ###
IOS_FIREBASE_APP_ID=1:************:ios:5d3b80605bdb6325c71b47
ANDROID_FIREBASE_APP_ID=1:************:android:91beeae8a4b725cbc71b47
FIREBASE_SERVICE_ACCOUNT_FILE='node/credentials/firebase_admin_dev.json'

### ANDROID PROJECT ###
ANDROID_PROJECT_DIR='android'
GRADLE_USE_LEGACY_BUILD=true
ANDROID_APK_PATH='android/app/build/outputs/apk/dev/release/app-dev-release.apk'
ANDROID_AAB_PATH='android/app/build/outputs/bundle/release/app-release.aab'
GRADLE_APP_IDENTIFIER='vn.fonos.mobile.dev'
GRADLE_APP_NAME='Fonos (dev)'
ANDROID_APPCENTER_APP_ID='fonos-android-development'
GOOGLE_SERVICES_FILE='google-services-dev.json'
GRADLE_KEYSTORE_PATH='app/keystore-dev.properties'
GRADLE_KEYSTORE_FILE_PATH='android/app/fonosmobile.dev.keystore'
ANDROID_FLAVOR="dev"


### CODEPUSH ###
IOS_CODEPUSH_DEPLOYMENT_NAME='Development'
ANDROID_CODEPUSH_DEPLOYMENT_NAME='Development'


### SECRETS ###

APP_ID=vn.fonos.mobile.dev
PACKAGE_NAME=vn.fonos.mobile.dev
IOS_APP_IDENTITY=**********

FIREBASE_DYNAMIC_LINK=fonosdev.page.link
FIREBASE_APP_DOMAIN=fonos-dev.firebaseapp.com
FIREBASE_REVERSED_CLIENT_ID=com.googleusercontent.apps.************-f5lb8ea1mg53oa4qbl44snlverd4nhhg
FIREBASE_API_KEY="AIzaSyAsdcjC0uCgy_6HLl1zG3kC6J05nmV2U6E"
FIREBASE_PROJECT_ID="fonos-dev"
FIRESTORE_DATABASE="asia-southeast-1"


FL_APPCENTER_API_TOKEN='4a88bc886dcf1b2f8bf4e3cc705bacb039b7f74d'

IOS_APPCENTER_APP_SECRET='b8050bd3-1365-4b2d-bfb1-3743ce5874f9'
ANDROID_APPCENTER_APP_SECRET='485e089b-e10f-4c62-99a5-e90e480631d4'

IOS_CODEPUSH_DEPLOYMENT_KEY='LKVuejlIK8Bu5pRVbGmL52NF3komC7g60khg3'
ANDROID_CODEPUSH_DEPLOYMENT_KEY='UlqNsHaJMhbtgPgMBL45uLExveDJyVrfDR75s'

FB_APP_ID=***************
FB_CLIENT_TOKEN=4cbb202edcf5535e92f2d33adc4d560e
FB_URL_SCHEME=fb***************
FB_APP_NAME='Fonos - Dev'

INSTABUG_API_KEY=e42eb2af662a5e2e1cc1547b0682d176

SERVER_URL=https://api-dev.fonos.dev
IMAGE_URL=https://cf-dev.fonos.dev

SUB_LITE_MONTHLY_ID='dev.subscription.monthly.lite'
SUB_LITE_ANNUALLY_ID='dev.subscription.annually.lite'
SUB_NEW_MONTHLY_ID='dev.subscription.monthly.premium'
ONE_CREDIT_ID='dev.bundle.1.credit'

SUB_MONTHLY_ID='vn.fonos.mobile.dev.subscription.monthly'
SUB_ANNUALLY_ID='vn.fonos.mobile.dev.subscription.annually'
SUB_TRIAL_ANNUALLY_ID='vn.fonos.mobile.dev.subscription.trial.annually'
SUB_MONTHLY_PRO_ID='vn.fonos.mobile.dev.sub.monthly.pro'
SUB_ANNUALLY_PRO_ID='vn.fonos.mobile.dev.sub.annually.pro'
MORE_3_CREDIT_ID='vn.fonos.mobile.dev.more.3.credit'
MORE_5_CREDIT_ID='vn.fonos.mobile.dev.more.5.credit'
MORE_10_CREDIT_ID='vn.fonos.mobile.dev.more.10.credit'
BUNDLE_3_CREDIT_ID='vn.fonos.mobile.dev.bundle.3.credit'
BUNDLE_5_CREDIT_ID='vn.fonos.mobile.dev.bundle.5.credit'
BUNDLE_10_CREDIT_ID='vn.fonos.mobile.dev.bundle.10.credit'
MORE_3_CREDIT_DISCOUNT_30_ID='vn.fonos.mobile.dev.more.3.credit.discount.30'
SUB_ANNUALLY_DISCOUNT_30_ID='vn.fonos.mobile.dev.subscription.annually.discount.30'
SUB_ANNUALLY_PRO_DISCOUNT_20_ID='vn.fonos.mobile.dev.sub.year.pro.20'
SUB_ANNUALLY_FREE_3_CREDITS_ID='fonos.dev.sub.year.free.3.credit'

CHURN_SUB_QUARTERLY_DISCOUNT_50_ID='fonos.dev.churn.sub.quarter.promo.50'
CHURN_SUB_ANNUALLY_DISCOUNT_30_ID='fonos.dev.churn.sub.year.promo.30'

APP_SCHEME=fonosdev
BRANDED_DOMAIN=app-dev.fonos.dev
APPS_FLYER_DOMAIN=fonos-dev.onelink.me
APPS_FLYER_KEY=uNqijbzRBoUr628MVEe3j5
APPS_FLYER_ONE_LINK_ID=/3lj6

REVENUECAT_PUBIC_KEY=YMtDVSLRAkvLfqTmcFNlHxuQVPPdpkYE

SEO_PAGE_URL=https://fonos.vn

MIXPANEL_PROJECT_TOKEN=ca113ecdb94eeb7454f11585b81d53d2

SKYEPUB_LICENSE_IOS=cc85-5cff-d28f-4de3
SKYEPUB_LICENSE_ANDROID=6dbf-9e37-84fc-c2b6

REALM_CREDENTIALS=6IlnP0frn1d2t3Qy64JwWDyehOEQX5o3pkFAQozoosPe5sO4luoljgwesjRJ2BKE
REALM_ID=fonos-realm-app-xlzpv

FLEXIBLE_SYNC_REALM_CREDENTIALS=2X2cbHVqjnZjSRus07IhfD3rQzgzJbHRgv54q8juHtX5u7Ogk71RZ2yYcfhRs8r9
FLEXIBLE_SYNC_REALM_ID=fonos-realm-playground-ehknk

AKAMAI_DD_URL=https://fonos-dd-dev.akamaized.net

ZENDESK_URL=https://zendesk-api-gateway-testing.fonos.dev
ZENDESK_API_URL=https://zendesk-api-gateway-testing.fonos.dev
ZENDESK_APP_ID=a5706b140000b80c4497acffc970a866c1032a9aae9b9fbe
ZENDESK_CLIENT_ID=mobile_sdk_client_b21a22014724d4beb34e
ZENDESK_CHAT_APP_ID=383463099656318977
ZENDESK_CHAT_ACCOUNT_KEY=cQmQi2Mkgk3NM5xXv1mqbjM1WvvyWQo2
ZENDESK_API_TOKEN=DvPNjbDmffSALUsC9shUzlsd5KoqGIcExbee5S59
ZENDESK_AGENT_USERNAME=<EMAIL>

REALM_WEB_APP_ID=fonos-realm-web-app-upocz
REALM_WEB_API_KEY=nkZGOhTfzf2aFNQnSDWLmZetWigBpr3C1d7VPiBaHZjAACklBYBEvxhneJaFQ7u6
REALM_WEB_APP_HOSTNAME=https://ap-southeast-1.aws.realm.mongodb.com
GRAPHQL_URL=https://ap-southeast-1.aws.realm.mongodb.com/api/client/v2.0/app/fonos-realm-web-app-upocz/graphql

SENTRY_DSN=https://<EMAIL>/5730578

WEBSITE_URL=http://localhost:3000

CLEVERTAP_ACCOUNT_ID=TEST-R9K-7W7-756Z
CLEVERTAP_TOKEN=TEST-a0a-3c2
CLEVERTAP_REGION=sg1

CLOUD_CDN_URL=https://cdn-dev.fonos.dev

ONESIGNAL_APP_ID=************************************

GOOGLE_SIGN_IN_WEB_CLIENT_ID=************-bmm0kv1v9medgfevllp6nedc76fc6sej.apps.googleusercontent.com

SENTRY_AUTH_TOKEN=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