fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### set_build_numbers_to_current_timestamp

```sh
[bundle exec] fastlane set_build_numbers_to_current_timestamp
```



----


## iOS

### ios restore_files

```sh
[bundle exec] fastlane ios restore_files
```



### ios build

```sh
[bundle exec] fastlane ios build
```



### ios build_to_simulator

```sh
[bundle exec] fastlane ios build_to_simulator
```



### ios certificates

```sh
[bundle exec] fastlane ios certificates
```



### ios sync_devices_and_profiles

```sh
[bundle exec] fastlane ios sync_devices_and_profiles
```



### ios deploy_to_firebase

```sh
[bundle exec] fastlane ios deploy_to_firebase
```



### ios deploy_to_testflight

```sh
[bundle exec] fastlane ios deploy_to_testflight
```



### ios upload_sentry_dsym

```sh
[bundle exec] fastlane ios upload_sentry_dsym
```



### ios generate_test_app

```sh
[bundle exec] fastlane ios generate_test_app
```



### ios run_simulator

```sh
[bundle exec] fastlane ios run_simulator
```



### ios upload_device_farm

```sh
[bundle exec] fastlane ios upload_device_farm
```



### ios deploy

```sh
[bundle exec] fastlane ios deploy
```



----


## Android

### android build

```sh
[bundle exec] fastlane android build
```



### android generate_test_app

```sh
[bundle exec] fastlane android generate_test_app
```



### android upload_device_farm

```sh
[bundle exec] fastlane android upload_device_farm
```



### android deploy_to_firebase

```sh
[bundle exec] fastlane android deploy_to_firebase
```



### android deploy_to_playstore

```sh
[bundle exec] fastlane android deploy_to_playstore
```



### android deploy

```sh
[bundle exec] fastlane android deploy
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
