fastlane_require 'dotenv'
fastlane_require 'spaceship'
require 'httparty'
require 'json'
release_notes_command = "git log HEAD --pretty=format:\"%s\" -1"


lane :set_build_numbers_to_current_timestamp do |options|
  incremented_build_number = Time.now.to_i.to_s
  `sed -i -e "s#.*IOS_VERSION_BUILD_NUMBER=.*#IOS_VERSION_BUILD_NUMBER='#{incremented_build_number}'#g" .env`
  `sed -i -e "s#.*ANDROID_VERSION_CODE=.*#ANDROID_VERSION_CODE='#{incremented_build_number}'#g" .env`
  ENV['IOS_VERSION_BUILD_NUMBER'] = incremented_build_number
  ENV['ANDROID_VERSION_CODE'] = incremented_build_number
end

# JS Environments

before_all do |lane, options|
  Dotenv.load(".env.#{ENV['ENV']}")
end

after_all do |lane, options|
  if File.exist?('../src/environment/index.js.back')
    restore_file(path: 'src/environment/index.js')
  end
end

error do |lane, exception, options|
  if File.exist?('../src/environment/index.js.back')
    restore_file(path: 'src/environment/index.js')
  end
end

### IOS ###

platform :ios do

  before_all do |lane, options|
    setup_circle_ci
    ENV["FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT"] = "180"
    ENV["FASTLANE_XCODE_LIST_TIMEOUT"] = "180"
#     ENV["FASTLANE_ITUNES_TRANSPORTER_USE_SHELL_SCRIPT"]="1"
#     ENV["FASTLANE_ITUNES_TRANSPORTER_PATH"]="/Applications/Transporter.app/Contents/itms"
  end

  lane :restore_files do |options|
    plist_path = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PLIST_PATH']}"
    if File.exist?("../#{plist_path}.back")
      restore_file(path: plist_path)
    end
    pbxproj_path="#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PROJECT_NAME']}.xcodeproj/project.pbxproj"
    if File.exist?("../#{pbxproj_path}.back")
      restore_file(path: pbxproj_path)
    end
  end

  error do |lane, exception, options|
    restore_files
  end

  lane :build do |options|
#   Obfuscate drm key file
    sh "cd .. && ./scripts/drm-protect.sh"

    plist_full_path = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PLIST_PATH']}"
    backup_file(path: plist_full_path)
    xcodeproj = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PROJECT_NAME']}.xcodeproj"
    xcworkspace = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PROJECT_NAME']}.xcworkspace"
    pbxproj_full_path="#{xcodeproj}/project.pbxproj"

    # !!! Path to the folder that you will cache on CI !!!
    ios_derived_data_path = File.expand_path("../.local_derived_data")
    cache_folder = File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/BuildProductsPath/#{ENV['IOS_CONFIGURATION']}-iphoneos")

    backup_file(path: pbxproj_full_path)

    update_info_plist(
      xcodeproj: xcodeproj,
      plist_path: ENV['IOS_PLIST_PATH'],
      block: lambda { |plist|
        plist['CFBundleName'] = ENV['IOS_APP_NAME']
        plist['CFBundleDisplayName'] = ENV['IOS_APP_NAME']
        plist['CFBundleShortVersionString'] = ENV['IOS_VERSION']
        plist['CFBundleVersion'] = ENV['IOS_VERSION_BUILD_NUMBER']
      },
    )
    update_info_plist(
      xcodeproj: xcodeproj,
      plist_path: 'NotificationService/Info.plist',
      block: lambda { |plist|
        plist['CFBundleShortVersionString'] = ENV['IOS_VERSION']
        plist['CFBundleVersion'] = ENV['IOS_VERSION_BUILD_NUMBER']
      },
    )

    profile_env_name = "sigh_#{ENV['IOS_APP_ID']}_#{ENV['MATCH_TYPE']}_profile-name"
    profile_clevertap_env_name = "sigh_#{ENV['IOS_APP_ID']}.NotificationService_#{ENV['MATCH_TYPE']}_profile-name"

    update_code_signing_settings(
      use_automatic_signing: false,
      path: "#{xcodeproj}",
      team_id: ENV['IOS_TEAM_ID'],
      bundle_identifier: ENV['IOS_APP_ID'],
      profile_name: "#{ENV[profile_env_name]}",
      code_sign_identity: ENV['MATCH_TYPE'] == 'development' ? 'iPhone Developer' : 'iPhone Distribution',
      targets: ['FonosMobile']
    )

    update_code_signing_settings(
      use_automatic_signing: false,
      path: "#{xcodeproj}",
      team_id: ENV['IOS_TEAM_ID'],
      bundle_identifier: "#{ENV['IOS_APP_ID']}.NotificationService",
      profile_name: "#{ENV[profile_clevertap_env_name]}",
      code_sign_identity: ENV['MATCH_TYPE'] == 'development' ? 'iPhone Developer' : 'iPhone Distribution',
      targets: ['NotificationService']
    )

    export_method = 'app-store'
    if ENV['MATCH_TYPE'] == 'adhoc' then
      export_method = 'ad-hoc'
    elsif ENV['MATCH_TYPE'] == 'development' then
      export_method = 'development'
    end

    # Step 0) Check if cache exists

    if(File.exist?(cache_folder))
      # Step 1) Apply a fix of "Copy Pods Resources" Build Phase

      # Before:
      # "${PODS_ROOT}/Target Support Files/Pods-MyApp/Pods-MyApp-resources.sh"
      #
      # After:
      # BUILT_PRODUCTS_DIR=/a/b/c "${PODS_ROOT}/Target Support Files/Pods-MyApp/Pods-MyApp-resources.sh"

      fastlane_require 'xcodeproj'
      project = Xcodeproj::Project.open("../#{xcodeproj}")
      target = project.targets.select { |target| target.name == ENV['IOS_PROJECT_NAME'] }.first
      phase = target.shell_script_build_phases.select { |phase| phase.name && phase.name.include?('Copy Pods Resources') }.first
      if (!phase.shell_script.start_with?('BUILT_PRODUCTS_DIR'))
        phase.shell_script = "BUILT_PRODUCTS_DIR=#{cache_folder} #{phase.shell_script}"
        project.save()
      end

      # Step 2) Build only .xcodeproj
      gym(
        clean: false,
        silent: true,
        project: xcodeproj,
        scheme: ENV['IOS_SCHEME'],
        configuration: ENV['IOS_CONFIGURATION'],
        export_method: export_method,
        destination: 'generic/platform=iOS',
        export_options: {
          compileBitcode: false,
          uploadBitcode: false,
          uploadSymbols: false
        },
        xcargs: [
            # Step 3) Provide paths where xcode can't find pods binaries
            "PODS_CONFIGURATION_BUILD_DIR=#{cache_folder}",
            "FRAMEWORK_SEARCH_PATHS='#{cache_folder} $(inherited)'",
            "LIBRARY_SEARCH_PATHS='#{cache_folder} $(inherited)'",
            "SWIFT_INCLUDE_PATHS=#{cache_folder}"
        ].join(" ")
      )
    else

      # Step 4) Build full app .xcworkspace
      gym(
        silent: true,
        scheme: ENV['IOS_SCHEME'],
        workspace: xcworkspace,
        derived_data_path: ios_derived_data_path,
        export_method: export_method,
        configuration: ENV['IOS_CONFIGURATION'],
        clean: true
      )


      # Step 5) Remove not a Pods binaries to reduce cache size
      require 'fileutils';
      dirs = [
        File.expand_path("#{ios_derived_data_path}/info.plist"),
        File.expand_path("#{ios_derived_data_path}/Logs"),
        File.expand_path("#{ios_derived_data_path}/SourcePackages"),
        File.expand_path("#{ios_derived_data_path}/ModuleCache.noindex"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/IntermediateBuildFilesPath/#{ENV['IOS_PROJECT_NAME']}.build"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/IntermediateBuildFilesPath/XCBuildData"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/BuildProductsPath/SwiftSupport"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/PrecompiledHeaders")
      ]
      dirs.each { |dir| FileUtils.rm_rf(dir) }
    end

    restore_files
    sh "cd .. && ./scripts/drm-restore.sh"
  end

  lane :build_to_simulator do |options|
    plist_full_path = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PLIST_PATH']}"
    backup_file(path: plist_full_path)
    xcodeproj = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PROJECT_NAME']}.xcodeproj"
    xcworkspace = "#{ENV['IOS_PROJECT_PATH']}/#{ENV['IOS_PROJECT_NAME']}.xcworkspace"
    pbxproj_full_path="#{xcodeproj}/project.pbxproj"

    # !!! Path to the folder that you will cache on CI !!!
    ios_derived_data_path = File.expand_path("../.local_derived_data")
    cache_folder = File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/BuildProductsPath/#{ENV['IOS_CONFIGURATION']}-iphonesimulator")

    backup_file(path: pbxproj_full_path)

    update_info_plist(
      xcodeproj: xcodeproj,
      plist_path: ENV['IOS_PLIST_PATH'],
      block: lambda { |plist|
        plist['CFBundleName'] = ENV['IOS_APP_NAME']
        plist['CFBundleDisplayName'] = ENV['IOS_APP_NAME']
        plist['CFBundleShortVersionString'] = ENV['IOS_VERSION']
        plist['CFBundleVersion'] = ENV['IOS_VERSION_BUILD_NUMBER']
      },
    )
    update_info_plist(
      xcodeproj: xcodeproj,
      plist_path: 'NotificationService/Info.plist',
      block: lambda { |plist|
        plist['CFBundleShortVersionString'] = ENV['IOS_VERSION']
        plist['CFBundleVersion'] = ENV['IOS_VERSION_BUILD_NUMBER']
      },
    )

    profile_env_name = "sigh_#{ENV['IOS_APP_ID']}_#{ENV['MATCH_TYPE']}_profile-name"
    profile_clevertap_env_name = "sigh_#{ENV['IOS_APP_ID']}.NotificationService_#{ENV['MATCH_TYPE']}_profile-name"

    update_code_signing_settings(
      use_automatic_signing: false,
      path: "#{xcodeproj}",
      team_id: ENV['IOS_TEAM_ID'],
      bundle_identifier: ENV['IOS_APP_ID'],
      profile_name: "#{ENV[profile_env_name]}",
      code_sign_identity: ENV['MATCH_TYPE'] == 'development' ? 'iPhone Developer' : 'iPhone Distribution',
      targets: ['FonosMobile']
    )

    update_code_signing_settings(
      use_automatic_signing: false,
      path: "#{xcodeproj}",
      team_id: ENV['IOS_TEAM_ID'],
      bundle_identifier: "#{ENV['IOS_APP_ID']}.NotificationService",
      profile_name: "#{ENV[profile_clevertap_env_name]}",
      code_sign_identity: ENV['MATCH_TYPE'] == 'development' ? 'iPhone Developer' : 'iPhone Distribution',
      targets: ['NotificationService']
    )
    # Step 0) Check if cache exists

    if(File.exist?(cache_folder))
      # Step 1) Apply a fix of "Copy Pods Resources" Build Phase

      # Before:
      # "${PODS_ROOT}/Target Support Files/Pods-MyApp/Pods-MyApp-resources.sh"
      #
      # After:
      # BUILT_PRODUCTS_DIR=/a/b/c "${PODS_ROOT}/Target Support Files/Pods-MyApp/Pods-MyApp-resources.sh"

      fastlane_require 'xcodeproj'
      project = Xcodeproj::Project.open("../#{xcodeproj}")
      target = project.targets.select { |target| target.name == ENV['IOS_PROJECT_NAME'] }.first
      phase = target.shell_script_build_phases.select { |phase| phase.name && phase.name.include?('Copy Pods Resources') }.first
      if (!phase.shell_script.start_with?('BUILT_PRODUCTS_DIR'))
        phase.shell_script = "BUILT_PRODUCTS_DIR=#{cache_folder} #{phase.shell_script}"
        project.save()
      end

      # Step 2) Build only .xcodeproj
      gym(
        clean: false,
        silent: false,
        project: xcodeproj,
        scheme: ENV['IOS_SCHEME'],
        configuration: ENV['IOS_CONFIGURATION'],
        destination: 'generic/platform=iOS Simulator',
        sdk: "iphonesimulator",
        skip_package_ipa: true,
        archive_path: "#{ios_derived_data_path}/Archive",
        xcargs: [
            "ONLY_ACTIVE_ARCH=NO",
            # Step 3) Provide paths where xcode can't find pods binaries
            "PODS_CONFIGURATION_BUILD_DIR=#{cache_folder}",
            "FRAMEWORK_SEARCH_PATHS='#{cache_folder} $(inherited)'",
            "LIBRARY_SEARCH_PATHS='#{cache_folder} $(inherited)'",
            "SWIFT_INCLUDE_PATHS=#{cache_folder}"
        ].join(" ")
      )

      app_dir = "#{ios_derived_data_path}/Archive.xcarchive/Products/Applications/#{ENV['IOS_PROJECT_NAME']}.app"
      Actions.sh("xcrun simctl install booted #{app_dir}")
      Actions.sh("xcrun simctl launch booted #{ENV['IOS_APP_ID']}")

      require 'fileutils';
       dirs = [
          File.expand_path("#{ios_derived_data_path}/Archive.xcarchive")
       ]
       dirs.each { |dir| FileUtils.rm_rf(dir) }
    else

      # Step 4) Build full app .xcworkspace
      gym(
        silent: false,
        scheme: ENV['IOS_SCHEME'],
        workspace: xcworkspace,
        derived_data_path: ios_derived_data_path,
        configuration: ENV['IOS_CONFIGURATION'],
        clean: true,
        destination: 'generic/platform=iOS Simulator',
        sdk: "iphonesimulator",
        archive_path: "#{ios_derived_data_path}/Archive",
        xcargs: [
            "ONLY_ACTIVE_ARCH=NO"
        ].join(" "),
        skip_package_ipa: true,
      )

      app_dir = "#{ios_derived_data_path}/Archive.xcarchive/Products/Applications/#{ENV['IOS_PROJECT_NAME']}.app"
      Actions.sh("xcrun simctl install booted #{app_dir}")
      Actions.sh("xcrun simctl launch booted #{ENV['IOS_APP_ID']}")

      # Step 5) Remove not a Pods binaries to reduce cache size
      require 'fileutils';
      dirs = [
        File.expand_path("#{ios_derived_data_path}/Archive.xcarchive"),
        File.expand_path("#{ios_derived_data_path}/info.plist"),
        File.expand_path("#{ios_derived_data_path}/Logs"),
        File.expand_path("#{ios_derived_data_path}/SourcePackages"),
        File.expand_path("#{ios_derived_data_path}/ModuleCache.noindex"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/IntermediateBuildFilesPath/#{ENV['IOS_PROJECT_NAME']}.build"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/IntermediateBuildFilesPath/XCBuildData"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/BuildProductsPath/SwiftSupport"),
        File.expand_path("#{ios_derived_data_path}/Build/Intermediates.noindex/ArchiveIntermediates/#{ENV['IOS_SCHEME']}/PrecompiledHeaders")
      ]
      dirs.each { |dir| FileUtils.rm_rf(dir) }

    end
    restore_files

  end

  lane :certificates do |options|
    match(
        app_identifier: [
          ENV['IOS_APP_ID'],
          ENV['IOS_APP_ID'] + '.NotificationService'
        ],
        shallow_clone: true,
        clone_branch_directly: true,
        git_url: ENV['MATCH_GIT_URL'],
        git_branch: ENV['MATCH_GIT_BRANCH'],
        api_key_path: 'fastlane/api_key.json', # Use API Key for auth
        team_id: ENV['IOS_TEAM_ID'],
        team_name: ENV['IOS_ITC_TEAM_NAME'],
        type: ENV['MATCH_TYPE'],
        readonly: false # Allow writes as per revised plan
      )
  end

  lane :sync_devices_and_profiles do |options|
    UI.message("Starting device registration and profile sync...")

    # --- Fetch Devices from Airtable ---
    airtable_api_key = '**********************************************************************************'
    airtable_base_id = 'app98JzfXJiQeIXBG'
    airtable_table_name = 'tblZftK8y0ZOEc069'

    unless airtable_api_key && airtable_base_id && airtable_table_name
      UI.user_error!("Airtable environment variables not set (AIRTABLE_API_KEY, AIRTABLE_BASE_ID, AIRTABLE_TABLE_NAME)")
    end

    airtable_url = "https://api.airtable.com/v0/#{airtable_base_id}/#{airtable_table_name}"
    headers = { "Authorization" => "Bearer #{airtable_api_key}" }
    
    begin
      response = HTTParty.get(airtable_url, headers: headers)
      unless response.success?
        UI.user_error!("Failed to fetch devices from Airtable: #{response.code} #{response.message}")
      end
      
      parsed_response = JSON.parse(response.body)
      records = parsed_response['records'] || []
      
      # ** IMPORTANT: Adjust 'Device ID' and 'Device Name' if your Airtable column names differ **
      # Build a Hash { 'Device Name' => 'Device ID', ... } for the 'devices' parameter
      devices_hash = records.each_with_object({}) do |record, hash|
        fields = record['fields']
        device_name = fields['Device Name']
        device_id = fields['Device ID']
        hash[device_name] = device_id if device_id && device_name
      end

      UI.message("Fetched #{devices_hash} devices from Airtable.")
    rescue => e
      UI.user_error!("Error fetching or parsing Airtable data: #{e.message}")
    end
    # --- End Fetch Devices ---

    register_devices(
      devices: devices_hash, # Pass the Hash directly
      api_key_path: 'fastlane/api_key.json',
      team_id: ENV['IOS_TEAM_ID']
    )

    # Update match profiles, forcing regeneration if new devices were added
    match(
        api_key_path: 'fastlane/api_key.json',
        app_identifier: [
          ENV['IOS_APP_ID'],
          ENV['IOS_APP_ID'] + '.NotificationService'
        ],
        type: ENV['MATCH_TYPE'], # Ensure this is set correctly (e.g., 'development' or 'adhoc')
        git_url: ENV['MATCH_GIT_URL'],
        git_branch: ENV['MATCH_GIT_BRANCH'],
        team_id: ENV['IOS_TEAM_ID'],
        team_name: ENV['IOS_ITC_TEAM_NAME'],
        readonly: false, # Allow writing to repo
        force_for_new_devices: true, # Regenerate profile if devices changed
        shallow_clone: true,
        clone_branch_directly: true
      )
    UI.message("Device registration and profile sync completed.")
  end

  # lane :deploy_to_appcenter do |options|
  #   appcenter_upload(
  #     api_token: ENV['FL_APPCENTER_API_TOKEN'],
  #     owner_name: ENV['APPCENTER_USERNAME'],
  #     app_name: ENV['IOS_APPCENTER_APP_ID'],
  #     ipa: ENV['IOS_IPA_PATH'],
  #     release_notes: %x[#{release_notes_command}],
  #     mandatory_update: ENV['ENV'] != 'production'
  #   )
  # end

  lane :deploy_to_firebase do |options|
    firebase_app_distribution(
      app: ENV['IOS_FIREBASE_APP_ID'],
      googleservice_info_plist_path: ENV['GOOGLE_PLIST_FILE'],
      service_credentials_file: ENV['FIREBASE_SERVICE_ACCOUNT_FILE'],
      ipa_path: ENV['IOS_IPA_PATH'],
      groups: 'testers',
      release_notes: (ENV['IOS_APP_NAME'] || '') + ' ' + (ENV['IOS_VERSION'] || '') + ' ' + (%x[#{release_notes_command}] || ''),
      debug: true
    )
  end

  lane :deploy_to_testflight do |options|
    pilot(
      api_key_path: 'fastlane/api_key.json',
      app_identifier: ENV['IOS_APP_ID'],
      ipa: ENV['IOS_IPA_PATH'],
      skip_waiting_for_build_processing: true
    )
  end

  lane :upload_sentry_dsym do |options|
    sentry_debug_files_upload(
        auth_token: ENV['SENTRY_AUTH_TOKEN'],
        org_slug: 'fonos',
        project_slug: 'fonos-mobile',
        include_sources: true
    )
  end

  lane :generate_test_app do
    certificates
    build
    puts "IPA path: #{ENV['IOS_IPA_PATH']}"
  end

  lane :run_simulator do
    certificates
    build_to_simulator
  end

  lane :upload_device_farm do
    ENV['AWS_ACCESS_KEY_ID'] = ENV['AWS_DEVICE_FARM_KEY_ID']
    ENV['AWS_SECRET_ACCESS_KEY'] = ENV['AWS_DEVICE_FARM_SECRET_KEY']
    ENV['AWS_REGION'] = 'us-west-2'

    certificates
    build

    aws_device_farm(
        name: 'Fonos Mobile E2E',
        binary_path: ENV['IOS_IPA_PATH'],
        test_binary_path: './e2e/e2e.zip',
        test_package_type: 'APPIUM_NODE_TEST_PACKAGE',
        test_type: 'APPIUM_NODE',
        test_spec: 'arn:aws:devicefarm:us-west-2:262705137193:upload:789f8ed1-52a5-472d-8974-91209d4d6e7e/be2edb27-b4e9-42cc-8d2c-9d50f3c61b94',
        wait_for_completion: false,
        print_web_url_of_run: true,
        device_pool: 'iOS pool'
    )
  end

  lane :deploy do |options|
    if options[:codepush] then
      release_notes = %x[#{release_notes_command}] || ''
      sh "cd .. && appcenter codepush release-react -d #{ENV['IOS_CODEPUSH_DEPLOYMENT_NAME']} -a #{ENV['APPCENTER_USERNAME']}/#{ENV['IOS_APPCENTER_APP_ID']} --target-binary-version \"#{ENV['IOS_VERSION']}\" --description \"#{release_notes}\" --disable-duplicate-release-error --mandatory #{ENV['ENV'] != 'production'}"
    else
      # Sync devices and profiles before building for Development or AdHoc types
      if ENV['MATCH_TYPE'] == 'development' || ENV['MATCH_TYPE'] == 'adhoc'
        sync_devices_and_profiles
      end
      certificates # Fetch potentially updated certs/profiles (readonly: false)
      build # Build the app
      upload_sentry_dsym
      if ENV['DEPLOYMENT_PLATFORM'] === 'appcenter' then
        deploy_to_firebase
      elsif ENV['DEPLOYMENT_PLATFORM'] === 'appstore'
        deploy_to_testflight
      end
    end
  end
end

###  ANDROID  ###
platform :android do
  lane :build do |options|
    sh "cd .. && ./scripts/drm-protect.sh"
    gradle(
      task: ENV['ENV'] != 'production' ? "assemble" : "bundle",
      build_type: 'Release',
      flavor: ENV['ANDROID_FLAVOR'],
      project_dir: ENV['ANDROID_PROJECT_DIR']
    )
    sh "cd .. && ./scripts/drm-restore.sh"
  end

  lane :generate_test_app do
    sh "cd .. && ./scripts/drm-protect.sh"
    gradle(
      task: "assemble",
      build_type: 'Release',
      flavor: ENV['ANDROID_FLAVOR'],
      project_dir: ENV['ANDROID_PROJECT_DIR'],
    )
    sh "cd .. && ./scripts/drm-restore.sh"
    puts "APK path: #{ENV['ANDROID_APK_PATH']}"
  end


  lane :upload_device_farm do
    ENV['AWS_ACCESS_KEY_ID'] = ENV['AWS_DEVICE_FARM_KEY_ID']
    ENV['AWS_SECRET_ACCESS_KEY'] = ENV['AWS_DEVICE_FARM_SECRET_KEY']
    ENV['AWS_REGION'] = 'us-west-2'

    generate_test_app

    aws_device_farm(
        name: 'Fonos Mobile E2E',
        binary_path: ENV['ANDROID_APK_PATH'],
        test_binary_path: './e2e/e2e.zip',
        test_package_type: 'APPIUM_NODE_TEST_PACKAGE',
        test_type: 'APPIUM_NODE',
        test_spec: 'arn:aws:devicefarm:us-west-2:262705137193:upload:789f8ed1-52a5-472d-8974-91209d4d6e7e/0e15e9d3-a067-41e1-ad40-a4f6f68ee379',
        wait_for_completion: false,
        print_web_url_of_run: true,
        device_pool: 'Android Hight OS Version Pool'
    )
  end

  # lane :deploy_to_appcenter do |options|
  #   appcenter_upload(
  #     api_token: ENV['FL_APPCENTER_API_TOKEN'],
  #     owner_name: ENV['APPCENTER_USERNAME'],
  #     app_name: ENV['ANDROID_APPCENTER_APP_ID'],
  #     apk: ENV['GRADLE_APK_OUTPUT_PATH'],
  #     release_notes: %x[#{release_notes_command}],
  #     mandatory_update: ENV['ENV'] != 'production'
  #   )
  # end

  lane :deploy_to_firebase do |options|
    firebase_app_distribution(
      app: ENV['ANDROID_FIREBASE_APP_ID'],
      android_artifact_type: 'APK',
      service_credentials_file: ENV['FIREBASE_SERVICE_ACCOUNT_FILE'],
      android_artifact_path: ENV['GRADLE_APK_OUTPUT_PATH'],
      groups: 'testers',
      release_notes: (ENV['IOS_APP_NAME'] || '') + ' ' + (ENV['ANDROID_VERSION_NAME'] || '') + ' ' + (%x[#{release_notes_command}] || ''),
      debug: true
    )
  end

  lane :deploy_to_playstore do |options|
    supply(
      package_name: ENV['GRADLE_APP_IDENTIFIER'],
      track: 'internal',
      aab: ENV['GRADLE_AAB_OUTPUT_PATH'],
      json_key: ENV['ANDROID_PLAYSTORE_JSON_KEY_PATH'],
      skip_upload_apk: true
    )
  end

  lane :deploy do |options|
    if options[:codepush] then
      release_notes = %x[#{release_notes_command}] || ''
      sh "cd .. && appcenter codepush release-react -d #{ENV['ANDROID_CODEPUSH_DEPLOYMENT_NAME']} -a #{ENV['APPCENTER_USERNAME']}/#{ENV['ANDROID_APPCENTER_APP_ID']} --target-binary-version \"#{ENV['ANDROID_VERSION_NAME']}\" --description \"#{release_notes}\" --disable-duplicate-release-error --mandatory #{ENV['ENV'] != 'production'}"
    else
      build
      if ENV['DEPLOYMENT_PLATFORM'] === 'appcenter' then
        deploy_to_firebase
      elsif ENV['DEPLOYMENT_PLATFORM'] === 'appstore' then
        deploy_to_playstore
      end
    end
  end

end
