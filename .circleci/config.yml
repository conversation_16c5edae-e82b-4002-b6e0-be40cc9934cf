# prettier: disable

commands:
  ios_checkout:
    description: 'Checkout ios repo'
    steps:
      - checkout
      - run:
          name: Install brew dependencies
          command: |
            brew install gpg cmake pkg-config ccache xcbeautify
            sudo ln -sf $(which cmake) /usr/local/bin/cmake
            # Symlink clang/clang++ to ccache
            sudo ln -sf $(which ccache) /usr/local/bin/clang
            sudo ln -sf $(which ccache) /usr/local/bin/clang++
            sudo ln -sf $(which pkg-config) /usr/local/bin/pkg-config

  ios_node_yarn:
    description: 'Install node & yarn globally'
    steps:
      - run:
          name: Install yarn & node globally
          command: |
            nvm install v18.20.2 && nvm alias default v18.20.2
            brew uninstall yarn
            npm -g install yarn
            yarn set version 3.6.4

  ios_ruby:
    description: 'Install ruby globally'
    steps:
      - macos/switch-ruby:
          version: '3.1.6'

  ios_pods:
    description: 'Install pods'
    steps:
      # - restore_cache:
      #     key: ios-v4-{{ checksum "./yarn.lock" }}-{{ arch }}

      - run:
          name: pod install
          command: cd ios && bundle exec pod install --repo-update

      # - save_cache:
      #     key: ios-v4-{{ checksum "./yarn.lock" }}-{{ arch }}
      #     paths:
      #       - ios/Pods

  android_checkout:
    description: 'Checkout android repo'
    steps:
      - checkout
      - run:
          name: Install checkout requirements
          command: |
            sudo apt-get update
            sudo apt-get install git-lfs
            git lfs pull

  android_node_yarn:
    description: 'Install node & yarn globally'
    steps:
      - node/install:
          node-version: 'v18.20.2'
          install-yarn: true
      - run: yarn set version 3.6.4

  android_ruby:
    description: 'Install ruby globally'
    steps:
      - ruby/install:
          version: '3.1.6'

  android_sdk:
    description: 'Download android sdk'
    steps:
      - run:
          name: Download android sdk
          command: |
            cd .. 
            mkdir android-sdk
            cd android-sdk
            wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
            unzip commandlinetools-linux-8512546_latest.zip
            ls

      - run:
          name: Set env vars for android sdk tools
          command: |
            echo '
            export ANDROID_HOME=$HOME/android-sdk
            export PATH=$PATH:$HOME/android-sdk/cmdline-tools/bin
            export PATH=$PATH:$HOME/android-sdk/cmdline-tools/latest' >> envrc

      - run:
          name: Accept sdk licenses
          command: |
            yes | sdkmanager --sdk_root=${ANDROID_HOME} --licenses || exit 0
            yes | sdkmanager --sdk_root=${ANDROID_HOME} --update || exit 0

  bundle_install:
    description: 'Install ruby dependencies'
    steps:
      - restore_cache:
          key: bundle-v7-{{ checksum "./Gemfile.lock" }}-{{ arch }}

      - run: cd ./ && sudo gem install bundler && bundle check --path=vendor/bundle || bundle install --path=vendor/bundle --jobs=4 --retry=3

      - save_cache:
          key: bundle-v7-{{ checksum "./Gemfile.lock" }}-{{ arch }}
          paths:
            - ~/.bundle/
            - ./vendor/bundle

  yarn_install:
    description: 'Install node dependencies'
    steps:
      - run:
          name: yarn install
          command: |
            git config --global url."https://".insteadOf git://
            cd ./
            yarn install --immutable --immutable-cache

  unpack_secrets:
    description: 'Unpack secrets'
    steps:
      - run:
          name: Unpack secrets
          command: |
            cd ./
            yarn unpack-secrets -e ${ENV} -p ${STAGING_SECRETS_PASSPHRASE}

version: 2.1
orbs:
  slack: circleci/slack@5.1.1
  macos: circleci/macos@2.5.2
  node: circleci/node@7.1.0
  ruby: circleci/ruby@2.5.0
jobs:
  android:
    working_directory: ~/FonosMobile
    machine:
      image: ubuntu-2004:2023.10.1
    resource_class: large
    environment:
      BASH_ENV: envrc
      JAVA_OPTS: '-Xms512m -Xmx4g'
      GRADLE_OPTS: '-Xmx2048m -Dorg.gradle.daemon=false -Dorg.gradle.jvmargs="-Xmx4g -XX:+HeapDumpOnOutOfMemoryError"'
    steps:
      - android_checkout

      - android_ruby

      - android_node_yarn

      - android_sdk

      - bundle_install

      - yarn_install

      - unpack_secrets

      - restore_cache:
          key: >-
            gradle-v1-{{ arch }}-{{ checksum "./android/gradle/wrapper/gradle-wrapper.properties" }}
          name: Restore gradle cache

      - run:
          name: Build, Sign & Deploy
          no_output_timeout: 60m
          command: |
            cd ./
            yarn deploy -t hard -o android -e ${ENV}

      - save_cache:
          key: >-
            gradle-v1-{{ arch }}-{{ checksum "./android/gradle/wrapper/gradle-wrapper.properties" }}
          name: Save gradle cache
          paths:
            - ~/.gradle/caches
            - ~/.gradle/wrapper

      - slack/notify:
          event: fail
          template: basic_fail_1

      - slack/notify:
          event: pass
          template: basic_success_1

  ios:
    macos:
      xcode: 16.1.0
    resource_class: macos.m1.medium.gen1
    environment:
      LC_ALL: en_US.UTF-8
      LANG: en_US.UTF-8
      HOMEBREW_NO_AUTO_UPDATE: 1
    working_directory: ~/FonosMobile

    steps:
      - ios_checkout

      - ios_ruby

      - run:
          name: Export CCache Configuration
          command: |
            mkdir -p ~/.ccache
            echo 'export CCACHE_DIR="$HOME/.ccache"' >> $BASH_ENV
            echo 'export CCACHE_COMPRESS="1"' >> $BASH_ENV
            echo 'export CCACHE_MAXSIZE="4G"' >> $BASH_ENV
            echo 'export CCACHE_CPP2="true"' >> $BASH_ENV
            echo 'export CCACHE_HARDLINK="true"' >> $BASH_ENV
            echo 'export CCACHE_SLOPPINESS="file_macro,time_macros,include_file_mtime,include_file_ctime,file_stat_matches"' >> $BASH_ENV
            echo 'export CCACHE_LOGFILE="/tmp/ccache.log"' >> $BASH_ENV

            sudo chown -R $(whoami) $HOME/.ccache || echo "Failed to chown ccache dir, continuing..."
            ls -ld ~/.ccache # Verify permissions

            source $BASH_ENV

      - ios_node_yarn

      - yarn_install

      - bundle_install

      - unpack_secrets

      - ios_pods

      - restore_cache:
          key: ios-v17-pods-derived-data-{{ checksum "./ios/Podfile.lock" }}-{{ arch }}-{{ .Branch }}

      # Restore ccache - Use a more specific key including Podfile.lock, package.json, and Xcode version
      - restore_cache:
          keys:
            - ios-ccache-v2-{{ checksum "ios/Podfile.lock" }}-{{ checksum "package.json" }}
            # Fallback to the most recent cache if no exact match is found
            - ios-ccache-v2-

      - run:
          name: Match, Build, Sign & Deploy
          no_output_timeout: 60m
          command: |
            cd ./
            echo "=== Initial CCache Stats ==="
            ccache --zero-stats # Reset stats for this build
            ccache --show-stats
            echo "============================"
            yarn deploy -t hard -o ios -e ${ENV}
            echo "=== Final CCache Stats ==="
            ccache --show-stats # Show stats after build
            ccache --evict-older-than 14d # Clean up old cache entries
            echo "=========================="

      - run:
          command: |
            mkdir -p /tmp/build-logs
            cp ~/Library/Logs/gym/* /tmp/build-logs/
          when: on_fail
      - store_artifacts:
          path: /tmp/build-logs

      # Save ccache - Use the same specific key as restore_cache
      - save_cache:
          key: ios-ccache-v2-{{ checksum "ios/Podfile.lock" }}-{{ checksum "package.json" }}
          paths:
            - ~/.ccache # Cache ccache directory

      # Save DerivedData (keeping existing cache strategy)
      - save_cache:
          key: ios-v17-pods-derived-data-{{ checksum "./ios/Podfile.lock" }}-{{ arch }}-{{ .Branch }}
          paths:
            - .local_derived_data

      - slack/notify:
          event: fail
          template: basic_fail_1

      - slack/notify:
          event: pass
          template: basic_success_1

  ios_e2e:
    macos:
      xcode: 16.1.0
    resource_class: macos.m1.medium.gen1
    environment:
      LC_ALL: en_US.UTF-8
      LANG: en_US.UTF-8
      HOMEBREW_NO_AUTO_UPDATE: 1
    parameters:
      device-name:
        type: string
      os-version:
        type: string
    working_directory: ~/FonosMobile

    steps:
      - ios_checkout

      - ios_ruby

      - ios_node_yarn

      - yarn_install

      - bundle_install

      - unpack_secrets

      - ios_pods

      - macos/add-uitest-permissions

      - macos/preboot-simulator:
          device: << parameters.device-name >>
          version: << parameters.os-version >>
          platform: 'iOS'

      - macos/install-rosetta

      - restore_cache:
          key: ios-v17-pods-derived-data-{{ checksum "./ios/Podfile.lock" }}-{{ arch }}-{{ .Branch }}

      - run:
          name: Build app simulator
          command: |
            cd ./
            yarn run-test-app -o ios -e ${ENV}

      - save_cache:
          key: ios-v17-pods-derived-data-{{ checksum "./ios/Podfile.lock" }}-{{ arch }}-{{ .Branch }}
          paths:
            - .local_derived_data

      - run:
          name: Install Appium
          command: |
            npm install -g appium@2.1.3 --unsafe-perm=true --allow-root
            appium driver install xcuitest

      - run:
          name: Run Test
          command: |
            cd ./e2e
            CIRCLECI=true yarn test -o ios -e ${ENV} -d "<< parameters.device-name >>" -v "<< parameters.os-version >>"

      - store_test_results:
          path: ./e2e/test-results

  ios_device_farm:
    working_directory: ~/FonosMobile
    macos:
      xcode: 16.1.0
    resource_class: macos.m1.medium.gen1
    environment:
      LC_ALL: en_US.UTF-8
      LANG: en_US.UTF-8
      HOMEBREW_NO_AUTO_UPDATE: 1
    steps:
      - ios_checkout

      - ios_ruby

      - ios_node_yarn

      - yarn_install

      - bundle_install

      - unpack_secrets

      - ios_pods

      - restore_cache:
          key: ios-v17-pods-derived-data-{{ checksum "./ios/Podfile.lock" }}-{{ arch }}-{{ .Branch }}

      - run: yarn zip-test-package

      - run:
          name: Match, Build, Sign & Deploy
          command: |
            cd ./
            yarn run-device-farm -o ios -e ${ENV}

      - save_cache:
          key: ios-v17-pods-derived-data-{{ checksum "./ios/Podfile.lock" }}-{{ arch }}-{{ .Branch }}
          paths:
            - .local_derived_data

  android_device_farm:
    working_directory: ~/FonosMobile
    machine:
      image: ubuntu-2004:2023.10.1
    resource_class: large
    environment:
      BASH_ENV: envrc
      JAVA_OPTS: '-Xms512m -Xmx4g'
      GRADLE_OPTS: '-Xmx2048m -Dorg.gradle.daemon=false -Dorg.gradle.jvmargs="-Xmx4g -XX:+HeapDumpOnOutOfMemoryError"'
    steps:
      - android_checkout

      - android_ruby

      - android_node_yarn

      - android_sdk

      - bundle_install

      - yarn_install

      - unpack_secrets

      - restore_cache:
          key: >-
            gradle-{{ arch }}-{{ checksum "./android/gradle/wrapper/gradle-wrapper.properties" }}
          name: Restore gradle cache

      - run: yarn zip-test-package

      - run:
          name: Build, Sign & Deploy
          command: |
            cd ./
            yarn run-device-farm -o android -e ${ENV}

      - save_cache:
          key: >-
            gradle-{{ arch }}-{{ checksum "./android/gradle/wrapper/gradle-wrapper.properties" }}
          name: Save gradle cache
          paths:
            - ~/.gradle/caches
            - ~/.gradle/wrapper

parameters:
  manual_development:
    type: boolean
    default: false
  manual_staging:
    type: boolean
    default: false
  manual_production:
    type: boolean
    default: false
  e2e_staging:
    type: boolean
    default: false
  device_farm_staging:
    type: boolean
    default: false
  device_farm_production:
    type: boolean
    default: false
  device_farm_new_homepage:
    type: boolean
    default: false

workflows:
  version: 2

  e2e_staging:
    when: << pipeline.parameters.e2e_staging >>
    jobs:
      - ios_e2e:
          device-name: 'iPhone 14'
          os-version: '16.4'
          context:
            - mobile-staging
          filters:
            branches:
              only:
                - staging

  device_farm_staging:
    when: << pipeline.parameters.device_farm_staging >>
    jobs:
      - ios_device_farm:
          context:
            - mobile-staging
          filters:
            branches:
              only:
                - feature/automation-test
      - android_device_farm:
          context:
            - mobile-staging
          filters:
            branches:
              only:
                - staging

  device_farm_new_homepage:
    when: << pipeline.parameters.device_farm_staging >>
    jobs:
      - ios_device_farm:
          context:
            - mobile-production
          filters:
            branches:
              only:
                - feature/automation-test-new-homepage
      - android_device_farm:
          context:
            - mobile-production
          filters:
            branches:
              only:
                - feature/automation-test-new-homepage

  device_farm_production:
    when: << pipeline.parameters.device_farm_production >>
    jobs:
      - ios_device_farm:
          context:
            - mobile-production
          filters:
            branches:
              only:
                - master
      - android_device_farm:
          context:
            - mobile-production
          filters:
            branches:
              only:
                - master

  manual_development:
    when: << pipeline.parameters.manual_development >>
    jobs:
      - ios:
          context:
            - slack-notify
            - mobile-development
          filters:
            branches:
              only:
                - development
      - android:
          context:
            - slack-notify
            - mobile-development
          filters:
            branches:
              only:
                - development
  manual_staging:
    when: << pipeline.parameters.manual_staging >>
    jobs:
      - ios:
          context:
            - slack-notify
            - mobile-staging
          filters:
            branches:
              only:
                - staging
      - android:
          context:
            - slack-notify
            - mobile-staging
          filters:
            branches:
              only:
                - staging
  manual_production:
    when: << pipeline.parameters.manual_production >>
    jobs:
      - ios:
          context:
            - slack-notify
            - mobile-production
          filters:
            branches:
              only:
                - master
                - staging
                - sandbox
      - android:
          context:
            - slack-notify
            - mobile-production
          filters:
            branches:
              only:
                - master
                - staging
                - sandbox
  build-android-ios:
    jobs:
      - ios:
          context:
            - slack-notify
            - mobile-staging
          filters:
            branches:
              only:
                - staging
      - ios:
          context:
            - slack-notify
            - mobile-sandbox
          filters:
            branches:
              only:
                - sandbox
      - android:
          context:
            - slack-notify
            - mobile-staging
          filters:
            branches:
              only:
                - staging
      - android:
          context:
            - slack-notify
            - mobile-sandbox
          filters:
            branches:
              only:
                - sandbox
      - ios:
          context:
            - slack-notify
            - mobile-production
          filters:
            tags:
              only: /^release-.*/
            branches:
              ignore: /.*/
      - android:
          context:
            - slack-notify
            - mobile-production
          filters:
            tags:
              only: /^release-.*/
            branches:
              ignore: /.*/
