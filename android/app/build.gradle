apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: 'com.google.gms.google-services'

apply plugin: "com.facebook.react"
googleServices.disableVersionCheck = true
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'

project.ext.envConfigFiles = [
        dev       : "fastlane/.env.dev",
        staging   : "fastlane/.env.staging",
        sandbox   : "fastlane/.env.sandbox",
        production: "fastlane/.env.production",
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

def projectRoot = rootDir.getAbsoluteFile().getParentFile().getAbsolutePath()

import com.android.build.OutputFile

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/react-native-codegen
    // codegenDir = file("../node_modules/react-native-codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")
    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
    //
    // Added by install-expo-modules

    /* Autolinking */
    autolinkLibrariesWithApp()

    // If you want to compress the JS bundle (slower startup, less
    // space consumption)
    //   enableBundleCompression = true
    // If don't you want to compress the JS bundle (faster startup,
    // higher space consumption)

    enableBundleCompression = true

    // entryFile = file(["node", "-e", "require('expo/scripts/resolveAppEntry')", rootDir.getAbsoluteFile().getParentFile().getAbsolutePath(), "android", "absolute"].execute(null, rootDir).text.trim())
    // entryFile = file(["node", "-e", "require('expo/scripts/resolveAppEntry')", projectRoot, "android", "absolute"].execute(null, rootDir).text.trim())
    // cliFile = new File(["node", "--print", "require.resolve('@expo/cli')"].execute(null, rootDir).text.trim())
    // cliFile = new File(["node", "--print", "require.resolve('@expo/cli', { paths: [require.resolve('expo/package.json')] })"].execute(null, rootDir).text.trim())
    // bundleCommand = "export:embed"
}


// apply from: new File(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim(), "../react.gradle")

/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 *  Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
//  def useIntlJsc = true
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'
def work_version = "2.7.0"

/**
 * Private function to get the list of Native Architectures you want to build.
 * This reads the value from reactNativeArchitectures in your gradle.properties
 * file and works together with the --active-arch-only flag of react-native run-android.
 */

def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

def _applicationId = (project.env.get("GRADLE_APP_IDENTIFIER") ?: 'vn.fonos.mobile.dev') as String
def appName = project.env.get("GRADLE_APP_NAME") ?: 'Fonos (dev)'
def _versionCode = (System.getenv("ANDROID_VERSION_CODE") ?: "1") as Integer
def _versionName = System.getenv("ANDROID_VERSION_NAME") ?: "1.0.0"
def _useLegacyBuildSystem = (project.env.get("GRADLE_USE_LEGACY_BUILD") ?: "false") as Boolean

// keystore.properties file, in the rootProject folder.
def keystorePropertiesFile = rootProject.file(project.env.get("GRADLE_KEYSTORE_PATH") ?: 'app/keystore-dev.properties')
// Initialize a new Properties() object called keystoreProperties.
def keystoreProperties = new Properties()

// Load your keystore.properties file into the keystoreProperties object.
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))


project.ext.sentryCli = [
    logLevel: "debug"
]

apply plugin: "io.sentry.android.gradle"

sentry {
    // Enables or disables the automatic configuration of Native Symbols
    // for Sentry. This executes sentry-cli automatically so
    // you don't need to do it manually.
    // Default is disabled.
    uploadNativeSymbols = true

    // Enables or disables the automatic upload of the app's native source code to Sentry.
    // This executes sentry-cli with the --include-sources param automatically so
    // you don't need to do it manually.
    // This option has an effect only when [uploadNativeSymbols] is enabled.
    // Default is disabled.
    includeNativeSources = true

    // `@sentry/react-native` ships with compatible `sentry-android`
    // This option would install the latest version that ships with the SDK or SAGP (Sentry Android Gradle Plugin)
    // which might be incompatible with the React Native SDK
    // Enable auto-installation of Sentry components (sentry-android SDK and okhttp, timber and fragment integrations).
    // Default is enabled.
    autoInstallation {
        enabled = false
    }
}

apply from: new File(["node", "--print", "require.resolve('@sentry/react-native/package.json')"].execute().text.trim(), "../sentry.gradle")
android {
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion
    useLibrary 'org.apache.http.legacy'

    namespace "vn.fonos.mobile"

    defaultConfig {
        applicationId _applicationId
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode _versionCode
        versionName _versionName
        resValue "string", "build_config_package", "vn.fonos.mobile"
        resValue 'string', "CODE_PUSH_APK_BUILD_TIME", String.format("\"%d\"", System.currentTimeMillis())
        renderscriptTargetApi 23
        renderscriptSupportModeEnabled true
        missingDimensionStrategy 'react-native-camera', 'general'
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
    }

    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include(*reactNativeArchitectures())
        }
    }

    signingConfigs {
        config {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.config
            resValue "string", "app_name", appName
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.config
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            resValue "string", "app_name", appName
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir "$buildDir/intermediates/merged_native_libs/release/out/lib"
            }
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // https://developer.android.com/studio/build/configure-apk-splits.html
            def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(OutputFile.ABI)
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        defaultConfig.versionCode * 1000 + versionCodes.get(abi)
            }

        }
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging = _useLegacyBuildSystem
            pickFirsts += ['**/libc++_shared.so', '**/armeabi-v7a/libc++_shared.so', '**/x86/libc++_shared.so', '**/arm64-v8a/libc++_shared.so', '**/x86_64/libc++_shared.so']
        }
        
        // Fix this: https://github.com/margelo/react-native-quick-crypto?tab=readme-ov-file#android-build-errors
        pickFirst '**/libcrypto.so'

    }


    flavorDimensions "enviroment"
    productFlavors {
        dev {
            dimension "enviroment"
            applicationId "vn.fonos.mobile.dev"
        }
        staging {
            dimension "enviroment"
            applicationId "vn.fonos.mobile.staging"
        }
        sandbox {
            dimension "enviroment"
            applicationId "vn.fonos.mobile.staging"
        }
        production {
            dimension "enviroment"
            applicationId "vn.fonos.mobile"
        }
    }
}


dependencies {

    implementation group: 'com.zendesk', name: 'messaging', version: '5.2.3'
    implementation group: 'com.zendesk', name: 'support', version: '5.0.3'
    implementation group: 'com.zendesk', name: 'chat', version: '3.3.0'
    implementation group: 'com.zendesk', name: 'answerbot', version: '3.0.2'

    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    implementation 'androidx.multidex:multidex:2.0.1'

    // Firebase dependencies
    implementation 'me.leolin:ShortcutBadger:1.1.21@aar'
    implementation 'com.facebook.fresco:webpsupport:2.5.0'
    implementation 'com.facebook.android:facebook-android-sdk:latest.release'
    implementation "com.squareup.okhttp3:okhttp-urlconnection:4.9.2"

    //  ---- BEGIN CLEVERTAP DEPENDENCIES ----
    implementation 'com.clevertap.android:clevertap-android-sdk:6.2.1'
    implementation 'com.clevertap.android:clevertap-rendermax-sdk:1.0.3'
    implementation 'com.google.firebase:firebase-firestore:25.1.3'
    implementation 'com.google.firebase:firebase-messaging:23.0.6'
    implementation 'androidx.core:core:1.9.0'
    implementation 'androidx.fragment:fragment:1.3.6'
    // MANDATORY for App Inbox
    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.viewpager:viewpager:1.0.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    // For CleverTap Android SDK v3.6.4 and above add the following -
    implementation 'com.android.installreferrer:installreferrer:2.2'
    // Optional ExoPlayer Libraries for Audio/Video Inbox Messages. Audio/Video messages will be dropped without these dependencies
    // implementation 'com.google.android.exoplayer:exoplayer:2.15.1'
    // implementation 'com.google.android.exoplayer:exoplayer-hls:2.15.1'
    // implementation 'com.google.android.exoplayer:exoplayer-ui:2.15.1'
    // ---- END CLEVERTAP DEPENDENCIES -

    implementation 'com.jakewharton:process-phoenix:2.1.2'

    implementation files('libs/skyepub.jar')

    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")

    implementation "androidx.test:core:1.4.0"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1"
    implementation "androidx.lifecycle:lifecycle-viewmodel:2.5.1"
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:review-ktx:2.0.1'

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}


