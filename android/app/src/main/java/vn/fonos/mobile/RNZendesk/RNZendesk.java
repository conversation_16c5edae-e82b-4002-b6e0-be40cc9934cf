package vn.fonos.mobile.RNZendesk;

import android.app.Activity;
import android.content.Context;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.zendesk.service.ErrorResponse;
import com.zendesk.service.ZendeskCallback;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import zendesk.chat.Chat;
import zendesk.chat.ChatConfiguration;
import zendesk.chat.ChatEngine;
import zendesk.chat.PushNotificationsProvider;
import zendesk.configurations.Configuration;
import zendesk.core.AnonymousIdentity;
import zendesk.core.Identity;
import zendesk.core.JwtIdentity;
import zendesk.core.ProviderStore;
import zendesk.core.UserProvider;
import zendesk.messaging.MessagingActivity;
import zendesk.core.Zendesk;
import zendesk.support.CreateRequest;
import zendesk.support.CustomField;
import zendesk.support.Request;
import zendesk.support.RequestProvider;
import zendesk.support.Support;
import zendesk.support.guide.ArticleConfiguration;
import zendesk.support.guide.HelpCenterActivity;
import zendesk.support.guide.ViewArticleActivity;
import zendesk.support.request.RequestActivity;
import zendesk.support.requestlist.RequestListActivity;
import zendesk.answerbot.AnswerBot;
import zendesk.answerbot.AnswerBotEngine;
import zendesk.support.SupportEngine;

public class RNZendesk extends ReactContextBaseJavaModule {

    private static ReactApplicationContext reactContext;

    RNZendesk(ReactApplicationContext context) {
        super(context);
        reactContext = context;
    }

    private Activity getActivity() {
        return reactContext.getCurrentActivity();
    }

    @Override
    public String getName() {
        return "RNZendesk";
    }

    @ReactMethod
    public void initialize(ReadableMap config) {
        String appId = config.getString("appId");
        String clientId = config.getString("clientId");
        String url = config.getString("zendeskUrl");
        String accountKey = config.getString("accountKey");
        String chatAppId = config.getString("chatAppId");

        Context context = reactContext;

        Zendesk.INSTANCE.init(context, url, appId, clientId);
        Support.INSTANCE.init(Zendesk.INSTANCE);
        AnswerBot.INSTANCE.init(Zendesk.INSTANCE, Support.INSTANCE);
        Chat.INSTANCE.init(context, accountKey, chatAppId);
    }

    @ReactMethod
    public void setUserIdentity(ReadableMap visitorInfo) {
        String token = visitorInfo.getString("userId");
        Identity identity = new JwtIdentity(token);
        Zendesk.INSTANCE.setIdentity(identity);
    }

    @ReactMethod
    public void setUserAttribute(String attribute, String value) {
        ProviderStore providerStore = Zendesk.INSTANCE.provider();

        if (providerStore != null) {
            Map<String, String> userFields = new HashMap<>();
            userFields.put(attribute, value);

            providerStore.userProvider().setUserFields(userFields, new ZendeskCallback<Map<String, String>>() {
                @Override
                public void onSuccess(Map<String, String> stringStringMap) {
                    // success
                }

                @Override
                public void onError(ErrorResponse errorResponse) {
                    // error
                }
            });
        }
    }

    @ReactMethod
    public void showGuides() {
        Configuration articleConfig = ViewArticleActivity.builder()
                .withContactUsButtonVisible(false)
                .config();

        HelpCenterActivity.builder()
                .withContactUsButtonVisible(false)
                .withShowConversationsMenuButton(false)
                .show(this.getActivity(), articleConfig);
    }

    @ReactMethod
    public void showTicketList() {

        RequestListActivity.builder()
                .withContactUsButtonVisible(false)
                .show(this.getActivity());
    }

    @ReactMethod
    public void showTicket(String id) {
        RequestActivity.builder()
            .withRequestId(id)
            .show(this.getActivity());
    }

    @ReactMethod
    public void showTicketCreation(ReadableMap config) {
        String subject = config.getString("subject");
        ReadableArray tags = config.getArray("tags");
        CustomField categoryField = new CustomField(900012369046L, "suggestion");

        List<String> _tags = new ArrayList(tags.toArrayList());

        RequestActivity.builder()
                .withRequestSubject(subject)
                .withTags(_tags)
                .withCustomFields(Arrays.asList(categoryField))
                .show(this.getActivity());
    }

    @ReactMethod
    public void showLiveChat(ReadableMap config) {
        ChatConfiguration chatConfiguration = ChatConfiguration.builder()
                .withAgentAvailabilityEnabled(true)
                .withOfflineFormEnabled(true)
                .build();

        MessagingActivity.builder()
                .withBotLabelString("Fonos")
                .withEngines(
                        AnswerBotEngine.engine(),
                        ChatEngine.engine(),
                        SupportEngine.engine()
                ).show(this.getActivity(), chatConfiguration);
    }

    @ReactMethod
    public void registerForNotifications(String deviceToken) {
        PushNotificationsProvider provider = Chat.INSTANCE.providers().pushNotificationsProvider();
        if (provider != null) {
            provider.registerPushToken(deviceToken);
        }
    }

    @ReactMethod
    public void createTicket(ReadableMap config, Promise promise){
        CreateRequest request = new CreateRequest();
        RequestProvider provider = Support.INSTANCE.provider().requestProvider();



        String _subject = config.getString("subject");
        String _description = config.getString("description");
        ReadableArray tags = config.getArray("tags");
        List<String> _tags = new ArrayList(tags.toArrayList());

        request.setSubject(_subject);
        request.setTags(_tags);
        request.setDescription(_description);

        provider.createRequest(request, new ZendeskCallback<Request>() {
            @Override
            public void onSuccess(Request request) {
                promise.resolve(request.getId());
            }

            @Override
            public void onError(ErrorResponse errorResponse) {
                promise.reject(errorResponse.getReason());
            }
        });
    }

}