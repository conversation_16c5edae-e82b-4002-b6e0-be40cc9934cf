package vn.fonos.mobile.InAppReview;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.module.annotations.ReactModule;
import com.google.android.gms.tasks.Task;
import com.google.android.play.core.review.ReviewInfo;
import com.google.android.play.core.review.ReviewManager;
import com.google.android.play.core.review.ReviewManagerFactory;
import com.google.android.play.core.review.testing.FakeReviewManager;


@ReactModule(name = InAppReview.NAME)
public class InAppReview extends ReactContextBaseJavaModule {
    public static final String NAME = "InAppReview";

    private final ReactApplicationContext reactContext;

    public InAppReview(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @NonNull
    @Override
    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void rate(ReadableMap options, final Callback callback) {
        final ReviewManager manager = ReviewManagerFactory.create(this.reactContext);

        Task<ReviewInfo> request = manager.requestReviewFlow();
        request.addOnCompleteListener(requestTask -> {
            if (requestTask.isSuccessful()) {
                ReviewInfo reviewInfo = requestTask.getResult();
                Activity activity = getCurrentActivity();
                if (activity == null) {
                    callback.invoke(false, "getCurrentActivity() unsuccessful");
                    return;
                }
                Task<Void> flow = manager.launchReviewFlow(activity, reviewInfo);
                flow.addOnCompleteListener(flowTask -> {
                    if (requestTask.isSuccessful()) {
                        callback.invoke(true);
                    } else {
                        callback.invoke(false, "launchReviewFlow() unsuccessful");
                    }
                });
            } else {
                callback.invoke(false, "requestReviewFlow() unsuccessful");
            }
        });
    }
}
