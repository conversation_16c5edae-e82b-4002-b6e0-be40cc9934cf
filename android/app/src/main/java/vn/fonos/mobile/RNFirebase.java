package vn.fonos.mobile;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.net.Uri;
import android.util.Log;

import com.facebook.react.bridge.JSApplicationIllegalArgumentException;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;


import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.GZIPInputStream;

import com.google.firebase.firestore.FirebaseFirestore;


public class RNFirebase extends ReactContextBaseJavaModule {
  private static ReactApplicationContext reactContext;

  RNFirebase(ReactApplicationContext context) {
    super(context);
    reactContext = context;
  }

  private Activity getActivity() {
    return reactContext.getCurrentActivity();
  }

  @Override
  public String getName() {
    return "RNFirebase";
  }


  public static byte[] readAllBytes(InputStream inputStream) throws IOException {
      ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
      try (GZIPInputStream gzipInputStream = new GZIPInputStream(new BufferedInputStream(inputStream))) {
          byte[] buffer = new byte[1024];
          int bytesRead;
          while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
              byteArrayOutputStream.write(buffer, 0, bytesRead);
          }
      }
      return byteArrayOutputStream.toByteArray();
  }


  @ReactMethod
  public static void loadBundle(String schema, Promise promise) {
      try {
          Log.e("[BUNDLE] start ", schema);

          AssetManager assetManager = reactContext.getAssets();
          InputStream inputStream = assetManager.open("bundles/" + schema + "_bundle.firestore.gz.bundle");
           
          byte[] data = readAllBytes(inputStream);

          Log.e("[BUNDLE] loading ", schema);

          if (data != null) {
              FirebaseFirestore firestore;
              if (BuildConfig.FIRESTORE_DATABASE != null) {
                  firestore = FirebaseFirestore.getInstance("asia-southeast-1");
              } else {
                  firestore = FirebaseFirestore.getInstance();
              }
              firestore.loadBundle(data);
              promise.resolve(schema);
          } else {
              Log.e("[BUNDLE] loading fail ", schema);
             promise.reject("Error", "Error loading bundle data");
          }
      } catch (Exception e) {
          e.printStackTrace();
          Log.e("[BUNDLE] fail ", e.getLocalizedMessage());
          promise.reject("Error", "Failed to load bundle: " + e.getMessage());
      }
  }
}