package vn.fonos.mobile.RNHeadsetDetect;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.media.AudioDeviceInfo;
import android.media.AudioManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import java.io.File;
import java.util.Arrays;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;

public class RNHeadsetDetectModule extends ReactContextBaseJavaModule implements LifecycleEventListener {
    BroadcastReceiver receiver;

    private static ReactApplicationContext reactContext;

    public double TOTAL_RAM = 0.0;
    public double FREE_STORAGE = 0.0;
    public double TOTAL_STORAGE = 0.0;

    public RNHeadsetDetectModule(ReactApplicationContext context) {
        super(context);
        reactContext = context;
        headsetDetect();
    }

    @NonNull
    @Override
    public String getName() {
        return "RNHeadsetDetect";
    }

    @Override
    public void onHostPause() {
    }

    @Override
    public void onHostDestroy() {
    }

    private void sendEvent(String eventName, String deviceName) {
        if (!reactContext.hasActiveReactInstance()) {
            return;
        }

        WritableMap payload = Arguments.createMap();
        WritableArray deviceList = Arguments.createArray();
        if (!deviceName.isEmpty()) {
            deviceList.pushString(deviceName);
        }

        payload.putArray("devices", deviceList);
        
        reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, payload);
    }

    @Override
    public void onHostResume() {
        final Activity activity = getCurrentActivity();
        if (activity == null) {
            return;
        }
        final AudioManager audioManager = (AudioManager) activity.getSystemService(Context.AUDIO_SERVICE);

        try {
            AudioDeviceInfo[] devices = audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS);
            for (AudioDeviceInfo device : devices) {
                final int type = device.getType();
                if (type == AudioDeviceInfo.TYPE_BLUETOOTH_A2DP || type == AudioDeviceInfo.TYPE_BLUETOOTH_SCO) {
                    // Device is found
                    final String deviceName = device.getProductName().toString();
                    sendEvent("onChange", deviceName);
                    return;
                }
            }
        } catch (NoSuchMethodError e) {
        }

        // No devices found
        sendEvent("onChange", "");
    }

    private void headsetDetect() {
        final BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        IntentFilter intentFilter = new IntentFilter(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED);
        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        this.receiver = new BroadcastReceiver() {
            private static final String LOG_TAG = "BluetoothHeadsetDetect";

            @Override
            public void onReceive(Context context, Intent intent) {
                final String action = intent.getAction();
                if (action.equals(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED)) {
                    // Bluetooth headset connection state has changed
                    BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                    final int state = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTED);
                    if (state == BluetoothProfile.STATE_CONNECTED) {
                        // Device has connected, report it
                        if (ActivityCompat.checkSelfPermission(reactContext, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                            // TODO: Consider calling
                            //    ActivityCompat#requestPermissions
                            // here to request the missing permissions, and then overriding
                            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                            //                                          int[] grantResults)
                            // to handle the case where the user grants the permission. See the documentation
                            // for ActivityCompat#requestPermissions for more details.
                            return;
                        }
                        sendEvent("onChange", device.getName());
                    } else if (state == BluetoothProfile.STATE_DISCONNECTED) {
                        // Device has disconnected, report it
                        sendEvent("onChange", "");
                    }
                } else if (action.equals(BluetoothAdapter.ACTION_STATE_CHANGED)) {
                    final int state = intent.getIntExtra(BluetoothProfile.EXTRA_STATE,
                            BluetoothProfile.STATE_DISCONNECTED);
                    if (state == BluetoothProfile.STATE_DISCONNECTED) {
                        // Bluetooth is disabled
                        sendEvent("onChange", "");
                    }
                }
            }
        };

        // Subscribe for intents
        reactContext.registerReceiver(this.receiver, intentFilter);
        // Subscribe for lifecycle
        reactContext.addLifecycleEventListener(this);
    }
}