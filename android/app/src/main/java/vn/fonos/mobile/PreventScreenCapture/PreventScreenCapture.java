package vn.fonos.mobile.PreventScreenCapture;

import android.app.Activity;
import android.view.View;
import android.view.WindowManager;
import androidx.annotation.NonNull;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;

import static com.facebook.react.bridge.UiThreadUtil.runOnUiThread;

class PreventScreenCaptureView extends SimpleViewManager<View> {
    public static final String REACT_CLASS = "PreventScreenCapture";
    ReactApplicationContext context;

    PreventScreenCaptureView(ReactApplicationContext reactContext) {
        context = reactContext;
    }

    @NonNull
    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @NonNull
    @Override
    protected View createViewInstance(@NonNull ThemedReactContext themedReactContext) {
        // Return a dummy view or modify as per your requirement
        return new View(themedReactContext);
    }

    private void forbidScreenshot(Activity activity) {
        activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
    }

    private void allowScreenshot(Activity activity) {
        activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
    }

    @ReactProp(name = "enabled")
    public void setEnabled(View view, boolean enabled) {
        Activity currentActivity = context.getCurrentActivity();
        if (currentActivity != null) {
            runOnUiThread(() -> {
                if (enabled) {
                    forbidScreenshot(currentActivity);
                } else {
                    allowScreenshot(currentActivity);
                }
            });
        }
    }
}
