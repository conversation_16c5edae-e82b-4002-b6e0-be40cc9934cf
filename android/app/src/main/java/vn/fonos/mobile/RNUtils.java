package vn.fonos.mobile;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.jakewharton.processphoenix.ProcessPhoenix;

import java.io.File;
import java.util.Arrays;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class RNUtils extends ReactContextBaseJavaModule {
    public double TOTAL_RAM = 0.0;
    public double FREE_STORAGE = 0.0;
    public double TOTAL_STORAGE = 0.0;

    public RNUtils(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    private void getDeviceStorageInfo() {
        try {
            ActivityManager activityManager = (ActivityManager) getCurrentActivity().getSystemService(Context.ACTIVITY_SERVICE);

            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);

            TOTAL_RAM = memoryInfo.totalMem / (1024 * 1024); //In MB

            File path = Environment.getDataDirectory();
            StatFs stat = new StatFs(path.getPath());
            long blockSize = stat.getBlockSizeLong();
            long availableBlocks = stat.getAvailableBlocksLong();
            long totalBlocks = stat.getBlockCountLong();

            FREE_STORAGE = (availableBlocks * blockSize) / (1024 * 1024);
            TOTAL_STORAGE = (totalBlocks * blockSize) / (1024 * 1024);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static int getBatteryPercentage(Context context) {
        if (context != null) {
            IntentFilter iFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatus = context.registerReceiver(null, iFilter);
            int level = batteryStatus != null ? batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) : -1;
            int scale = batteryStatus != null ? batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1) : -1;
            float batteryPct = level / (float) scale;
            return (int) (batteryPct * 100);
        } else {
            return 100;
        }
    }

    private double getUsedCPU() throws IOException {
        String packageName = getReactApplicationContext().getPackageName();
        String[] commands = { "top", "-n", "1", "-q", "-oCMDLINE,%CPU", "-s2", "-b" };
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(Runtime.getRuntime().exec(commands).getInputStream())
        );
        String line;
        double cpuUsage = 0;
        while ((line = reader.readLine()) != null) {
            if (!line.contains(packageName)) continue;
            line = line.replace(packageName, "").replaceAll(" ", "");
            cpuUsage = Double.parseDouble(line);
            break;
        }
        reader.close();
        return cpuUsage;
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public String getDevicePerformance() {
        getDeviceStorageInfo();

        //Mid-end
        int performance = 1;
        try {
            ActivityManager activityManager = (ActivityManager) getCurrentActivity().getSystemService(Context.ACTIVITY_SERVICE);
            int batteryPercent = getBatteryPercentage(getReactApplicationContext());
            String abi;
            abi = Build.SUPPORTED_ABIS[0];

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S_V2) {
                boolean support64Bits = Arrays.asList(Build.SUPPORTED_64_BIT_ABIS).contains(abi);
                boolean isX86 = abi.equals("x86");
                boolean isSlowRam = activityManager.isLowRamDevice();
                int processorCount = Runtime.getRuntime().availableProcessors();
//                Log.d("[TAG] support64Bits", (!support64Bits && !isX86) + "");
//                Log.d("[TAG] isSlowRam", isSlowRam + "");
//                Log.d("[TAG] processorCount", processorCount + "");
                if ((!support64Bits && !isX86) || isSlowRam || processorCount < 8) {
                    //lowend
                    performance = 0;
                } else if (processorCount > 8) {
                    //highend
                    performance = 2;
                }
            } else {
                performance = 0;
            }

            if (batteryPercent < 20) {
                performance = performance - 1;
            }
            if (TOTAL_RAM > 0 && TOTAL_RAM < 8192) {
                performance = performance - 1;
            }
        } catch (Exception error) {
        }

        String result;

        if (performance >= 2) {
            result = "high-end";
        } else if (performance == 1) {
            result = "mid-end";
        } else {
            result = "low-end";
        }

        return result;
//        callback.invoke(result);
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public float getSoftMenuBarHeight() {
        if (hasPermanentMenuKey()) {
            return 0;
        }
        final Context ctx = getReactApplicationContext();
        final DisplayMetrics metrics = ctx.getResources().getDisplayMetrics();
        final int heightResId = ctx.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
        return heightResId > 0 ? ctx.getResources().getDimensionPixelSize(heightResId) / metrics.density : 0;
    }

    private boolean hasPermanentMenuKey() {
        final Context ctx = getReactApplicationContext();
        int id = ctx.getResources().getIdentifier("config_showNavigationBar", "bool", "android");
        return !(id > 0 && ctx.getResources().getBoolean(id));
    }

    @Override
    public String getName() {
        return "RNUtils";
    }

    @ReactMethod
    public void makeNativeError() throws Exception {
        throw new Exception("TEST EXCEPTION ON ANDROID");
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    public double getCPUUsage() {
        try {
            return getUsedCPU();
        } catch (IOException e) {
            e.printStackTrace();
            return 0;
        }
    }


    @ReactMethod
    public void restartApp() {
        ProcessPhoenix.triggerRebirth(getReactApplicationContext());
    }
}