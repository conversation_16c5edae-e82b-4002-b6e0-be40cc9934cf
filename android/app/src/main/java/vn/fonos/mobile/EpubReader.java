package vn.fonos.mobile;

import static com.facebook.react.bridge.UiThreadUtil.runOnUiThread;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.util.ArrayMap;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.ColorUtils;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.CatalystInstance;
import com.facebook.react.bridge.ObjectAlreadyConsumedException;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableNativeArray;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.ViewGroupManager;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.skytree.epub.Book;
import com.skytree.epub.BookInformation;
import com.skytree.epub.Caret;
import com.skytree.epub.ClickListener;
import com.skytree.epub.ConnectionListener;
import com.skytree.epub.ContentListener;
import com.skytree.epub.Highlight;
import com.skytree.epub.HighlightListener;
import com.skytree.epub.Highlights;
import com.skytree.epub.ItemRef;
import com.skytree.epub.NavPoint;
import com.skytree.epub.NavPoints;
import com.skytree.epub.PageInformation;
import com.skytree.epub.PageMovedListener;
import com.skytree.epub.PageTransition;
import com.skytree.epub.PagingInformation;
import com.skytree.epub.PagingListener;
import com.skytree.epub.PagingMode;
import com.skytree.epub.ReflowableControl;
import com.skytree.epub.ScriptListener;
import com.skytree.epub.SelectionListener;
import com.skytree.epub.SkyProvider;
import com.skytree.epub.State;
import com.skytree.epub.StateListener;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


import static java.lang.String.valueOf;
import static io.invertase.firebase.app.ReactNativeFirebaseApp.getApplicationContext;

import vn.fonos.mobile.BuildConfig;

class CustomArrayList<T> extends ArrayList<T> {
    @Override
    public T get(int index) {
        try {
            T prevObj = super.get(index);
            return prevObj;
        } catch (Exception e) {

        }
        return null;
    }

    public Object addVal(int index, T o) {
        if ((this.size() == 0 && index != 0) || index > this.size() + 1) return null;
        if (this.size() == 0) {
            return this.add(o);
        } else {
            T prevObj = this.get(index);

            if (prevObj == null) {
                return this.add(o);
            } else {
                return this.set(index, o);
            }
        }
    }
}

class CustomFont {
    public String fontFaceName;
    public String fontFileName;

    CustomFont(String faceName, String fileName) {
        this.fontFaceName = faceName;
        this.fontFileName = fileName;
    }

    public String getFullName() {
        String fullName = "";
        if (fontFileName == null || fontFileName.isEmpty()) {
            fullName = this.fontFaceName;
        } else {
            fullName = this.fontFaceName + "!!!/fonts/" + this.fontFileName;
        }
        return fullName;
    }
}

class EpubReaderModule extends ReactContextBaseJavaModule {
    public ReactApplicationContext thisThem = null;
    public static final String REACT_CLASS = "EpubReader";

    private static final String PREVENT_SCREENSHOT_ERROR_CODE = "PREVENT_SCREENSHOT_ERROR_CODE";

    EpubReaderModule(ReactApplicationContext context) {
        super(context);
        thisThem = context;
    }

    @NonNull
    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @ReactMethod
    public void forbidScreenshot(Promise promise) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    thisThem.getCurrentActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
                    promise.resolve("Done. Screenshot taking locked.");
                } catch (Exception e) {
                    promise.reject(PREVENT_SCREENSHOT_ERROR_CODE, "Forbid screenshot taking failure.");
                }
            }
        });
    }

    @ReactMethod
    public void allowScreenshot(Promise promise) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    thisThem.getCurrentActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
                    promise.resolve("Done. Screenshot taking unlocked.");
                } catch (Exception e) {
                    promise.reject(PREVENT_SCREENSHOT_ERROR_CODE, "Allow screenshot taking failure.");
                }
            }
        });
    }
}

public class EpubReader extends SimpleViewManager<RelativeLayout> {
    public static final String REACT_CLASS = "EpubReader";
    public ThemedReactContext thisThem = null;
    RelativeLayout ePubView;
    ReflowableControl eV;
    private CatalystInstance reactContext;
    String BookPath = "";
    boolean init = true;
    boolean isFirstTime = true;
    State currentState = State.NORMAL;
    PageInformation currentPage;
    Highlight currentHighlight;
    Highlights bookHighlights = new Highlights();

    @Override
    @NonNull
    public String getName() {
        return REACT_CLASS;
    }

    WritableArray chaptersList = Arguments.createArray();
    //    ArrayMap<Integer, WritableMap> chapterArrayMap = new ArrayMap<>();
    CustomArrayList<JSONObject> chapterArrayMap = new CustomArrayList<>();
    CustomArrayList<PagingInformation> pagingInformationList = new CustomArrayList<>();
    ArrayList<CustomFont> customFonts = new ArrayList<CustomFont>();


    @Override
    @NonNull
    public RelativeLayout createViewInstance(ThemedReactContext reactContext) {
        isFirstTime = true;
        thisThem = reactContext;
//        setup( );
        int pagePositionInBook = 0;
        if (eV == null) {

            eV = new ReflowableControl(thisThem.getCurrentActivity());
            SkyProvider skyProvider = new SkyProvider();
            eV.setScriptListener(new ScriptListener() {
                @Override
                public String getScriptForChapter(int i) {
                    return null;
                }

                @Override
                public String getStyleForChapter(int i) {
                    return " ul, ol { padding-left: 0; } li { list-style:none; } li::before { content: \"•  \" } ";
                }
            });
            String model = Build.MODEL;
            if (!model.contains("SM-N920")) {   // for note5, don't use software layer to avoid curling issue.
                eV.useSoftwareLayer();
            }
            eV.adjustContentWidth(true);
            eV.setSwipeEnabled(true);
            eV.setDelayFactor(0.25f);
            eV.setSystemSelectionEnabled(true);
            eV.setMaxSizeForBackground(1024);
            eV.setFingerTractionForSlide(false);
            eV.setCustomDrawHighlight(true);
            eV.setGlobalOffset(true);
            eV.setContentProvider(skyProvider);
            eV.setStartPositionInBook(pagePositionInBook);
            eV.setLicenseKey(BuildConfig.SKYEPUB_LICENSE_ANDROID);
            eV.setPagingMode(PagingMode.PAGING_NORMAL);
            eV.setNavigationAreaWidthRatio(0.2f);
            eV.setNavigationAreaEnabled(true);
            eV.setCustomDrawCaret(true);
            eV.setExtractText(true);
            eV.setScrollMode(false);
            eV.setPagingListener(new PagingDelegate());
            eV.setClickListener(new OnClickDelegate());
            eV.setStateListener(state -> currentState = state);
            eV.setPageMovedListener(new OnPageMovedDelegate());
            eV.setTTSEnabled(false);
            eV.setSigilStyleEnabled(false);
            eV.setHighlightListener(new HighlightDelegate());
            eV.setBookStyleEnabled(false);
            eV.setBookFontEnabled(true);
            eV.setRTL(false);
            eV.setDrawingHighlightOnFront(true);
            eV.setDoublePagedForLandscape(true);
            eV.setRotationLocked(true);
            eV.setScrollMode(false);
            eV.setCurlQuality(1.0f);
            eV.setSelectionListener(new SelectionDelegate());
            // if true, sdk will require you to draw the custom selector.
            eV.changeForegroundColor(Color.parseColor("#000000"));
            eV.setGlobalPagination(false);
//        eV.setAutoStartScan(true);
            eV.setHighlightRectsOptimized(true);
            // set ContentView and put FixedControl created into that.
            ePubView = new RelativeLayout(thisThem);
            ePubView.addView(eV);
            this.registerFonts();
            // set the Content view of Activity.
        }

        ePubView.invalidate();
        return ePubView;
    }


    public void setTransitionType(int transitionType) {
        if (transitionType == 0) {
            eV.setPageTransition(PageTransition.None);
        } else if (transitionType == 1) {
            eV.setPageTransition(PageTransition.Slide);
        } else if (transitionType == 2) {
            eV.setPageTransition(PageTransition.Curl);
        }
    }

    public void registerFonts() {
        this.registerCustomFont("EB Garamond", "EBGaramond.ttf");
        this.registerCustomFont("Literata TT", "LiterataTT.ttf");
        this.registerCustomFont("Quicksand", "Quicksand.ttf");
        this.registerCustomFont("Latin Modern Mono Light", "LMMonoLt10.ttc");
        this.registerCustomFont("SVN-Times New Roman", "SVN-TimesNewRoman.ttc");
    }

    public void registerCustomFont(String fontFaceName, String fontFileName) {
        customFonts.add(new CustomFont(fontFaceName, fontFileName));
    }

    // CustomFont
    public CustomFont getCustomFont(String fontName) {
        int len = customFonts.size();
        for (int i = 0; i < len; i++) {
            CustomFont cf = customFonts.get(i);
            if (customFonts.get(i).fontFaceName.equals(fontName)) {
                return customFonts.get(i);
            }
        }

        return new CustomFont("Literata TT", "LiterataTT.ttf");
    }

    JSONObject config = new JSONObject();

    private boolean isHexColor(String color) {
        // Regex pattern for hexadecimal color code
        String hexColorPattern = "^#([A-Fa-f0-9]{6})$";
        // Check if the color matches the pattern
        return color.matches(hexColorPattern);
    }

    @ReactProp(name = "config")
    public void setConfig(RelativeLayout ePubView, @Nullable ReadableMap cfg) {
        try {
            assert cfg != null;
            String font = cfg.getString("font");
            String backgroundColor = cfg.getString("backgroundColor");
            String textColor = cfg.getString("textColor");
            int fontSize = cfg.getInt("fontSize");
            int lineSpacing = cfg.getInt("lineSpacing");
            int transitionType = cfg.getInt("transitionType");
            double horizontalGapRatio = cfg.getDouble("horizontalGapRatio");
            double verticalGapRatio = cfg.getDouble("verticalGapRatio");
            if ((config.has("font") && !config.getString("font").equals(font)) || !config.has("font")) {
                CustomFont customFont = this.getCustomFont(font);
                String name = customFont.getFullName();

                eV.changeFont(name, fontSize);

                config.put("font", font);
            }
            if ((config.has("fontSize") && config.getInt("fontSize") != fontSize) || !config.has("fontSize")) {
                eV.changeFontSize(fontSize);
                config.put("fontSize", fontSize);
            }
            if ((config.has("lineSpacing") && config.getInt("lineSpacing") != lineSpacing) || !config.has("lineSpacing")) {
                eV.changeLineSpacing(lineSpacing);
                config.put("lineSpacing", lineSpacing);
            }
            if ((config.has("transitionType") && config.getInt("transitionType") != transitionType) || !config.has("transitionType")) {
                setTransitionType(transitionType);
                config.put("transitionType", transitionType);
            }
            if ((config.has("horizontalGapRatio") && config.getDouble("horizontalGapRatio") != horizontalGapRatio) || !config.has("horizontalGapRatio")) {
                eV.setHorizontalGapRatio(horizontalGapRatio);
                config.put("horizontalGapRatio", horizontalGapRatio);
            }
            if ((config.has("verticalGapRatio") && config.getDouble("verticalGapRatio") != verticalGapRatio) || !config.has("verticalGapRatio")) {
                eV.setVerticalGapRatio(verticalGapRatio);
                config.put("verticalGapRatio", verticalGapRatio);
            }

            if ((config.has("textColor") && config.getString("textColor") != textColor) || !config.has("textColor")) {
                if (isHexColor(textColor)) {
                    eV.changeForegroundColor(Color.parseColor(textColor));
                    config.put("textColor", textColor);
                }
            }
            if ((config.has("backgroundColor") && config.getString("backgroundColor") != backgroundColor) || !config.has("backgroundColor")) {
               if (isHexColor(backgroundColor)) {
                    eV.changeBackgroundColor(Color.parseColor(backgroundColor));
                    config.put("backgroundColor", backgroundColor);
                }
            }
            init = true;

//        eV.forceLayout();
            eV.reload();
            eV.repaintAll();
            ePubView.invalidate();
        } catch (Exception err) {
            Log.e("setConfig", err.getLocalizedMessage(), err);
        }
    }

    @ReactProp(name = "highlights")
    public void setHighlights(RelativeLayout ePubView, @Nullable ReadableArray cfg) {
        try {
            assert cfg != null;
            bookHighlights = new Highlights();
            for (int i = 0; i < cfg.size(); i++) {
                ReadableMap currentPage = cfg.getMap(i);
                ReadableArray allHighlights = currentPage.getArray("highlights");
                int chapterIndex = currentPage.getInt("chapterIndex");

                for (int j = 0; j < allHighlights.size(); j++) {
                    ReadableMap hl = allHighlights.getMap(j);
                    int bookCode = hl.getInt("bookCode");
                    int code = hl.getInt("code");
                    int startIndex = hl.getInt("startIndex");
                    int startOffset = hl.getInt("startOffset");
                    int endIndex = hl.getInt("endIndex");
                    int endOffset = hl.getInt("endOffset");
                    String text = hl.getString("text");
                    String note = hl.getString("note");
                    int color = hl.getInt("highlightColor");


                    Highlight hl2 = new Highlight();
                    hl2.bookCode = bookCode;
                    hl2.code = code;
                    hl2.chapterIndex = chapterIndex;
                    hl2.startIndex = startIndex;
                    hl2.startOffset = startOffset;
                    hl2.endIndex = endIndex;
                    hl2.endOffset = endOffset;
                    hl2.color = color;
                    hl2.text = text;
                    hl2.note = note;

                    bookHighlights.addHighlight(hl2);
                }
            }
        } catch (Exception err) {
            Log.e("highlights", err.getLocalizedMessage(), err);
        }
    }


    @Override
    public void receiveCommand(@NotNull RelativeLayout relativeLayout, String commandId, @Nullable ReadableArray args) {
        super.receiveCommand(relativeLayout, commandId, args);
        Log.e("DATA", commandId);
        if (eV != null) {
            switch (commandId) {
                case "nextPage":
                    eV.gotoNextPage();
                    break;
                case "prevPage":
                    eV.gotoPrevPage();
                    break;
                case "gotoPosition":
                    if (args != null) {
                        double ppb = args.getDouble(0);
                        eV.gotoPageByPagePositionInBook(ppb);
                    }
                    break;
                case "gotoPageByChapterIndex":
                    if (args != null) {
                        int chapterIndex = args.getInt(0);
                        int pageIndex = args.getInt(1);
                        if (currentPage != null){
                            if (currentPage.chapterIndex != chapterIndex){
                                NavPoints nps = eV.getNavPoints();
                                NavPoint targetNavPoint = nps.getNavPoint(chapterIndex);
                                for (int i = 0; i < nps.getSize(); i++) {
                                    if (nps.getNavPoint(i).chapterIndex == chapterIndex) {
                                        targetNavPoint = nps.getNavPoint(i);
                                        break;
                                    }
                                }
                                eV.gotoPageByNavPoint(targetNavPoint);
                                if (currentPage.pageIndex != pageIndex){
                                    Handler handler = new Handler();
                                    handler.postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            eV.gotoPageInChapter(pageIndex);
                                        }
                                    }, 500);
                                }
                            } else {
                                eV.gotoPageInChapter(pageIndex);
                            }
                        } else {
                            NavPoints nps = eV.getNavPoints();
                            NavPoint targetNavPoint = nps.getNavPoint(chapterIndex);
                            for (int i = 0; i < nps.getSize(); i++) {
                                if (nps.getNavPoint(i).chapterIndex == chapterIndex) {
                                    targetNavPoint = nps.getNavPoint(i);
                                    break;
                                }
                            }
                            eV.gotoPageByNavPoint(targetNavPoint);
                            Handler handler = new Handler();
                            handler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    eV.gotoPageInChapter(pageIndex);
                                }
                            }, 500);
                        }
                    }
                    break;
                case "gotoChapterIndex":
                    int index = args.getInt(0);
                    NavPoints nps = eV.getNavPoints();
                    NavPoint targetNavPoint = nps.getNavPoint(index);
                    eV.gotoPageByNavPoint(targetNavPoint);
                    break;
                case "destroy":
                    config = new JSONObject();
                    eV.destroy();
                    eV = null;
                    ePubView.removeAllViews();
                    break;

                case "highlight":
                    String hexColor = args.getString(0);
                    if (currentHighlight != null) {
                        int color = getColorByHex(hexColor);
                        eV.markSelection(color, "");
                        currentHighlight.color = color;
                    }
                    break;
                case "deleteHighlight":
                    onHighlight(currentHighlight, commandId);
                    sendHighlightsInPage();

                    break;
                case "updateHighlight":
                    try {
                        Highlight hl2 = getHighlight(args);
                        onHighlight(hl2, commandId);
                    } catch (Exception e) {
                        Log.e("React native", "Caught Exception: " + e.getMessage());
                    }

                    break;

                case "gotoHighlight":
                    Highlight hl = getHighlight(args);
                    eV.gotoPageByHighlight(hl);
                    break;
                default:
                    throw new IllegalStateException("Unexpected value: " + commandId);
            }

        }
    }

    Highlight getHighlight(ReadableArray args) {
        ReadableMap hl = args.getMap(0);

        int bookCode = hl.getInt("bookCode");
        int code = hl.getInt("code");
        int chapterIndex = hl.getInt("chapterIndex");
        int startIndex = hl.getInt("startIndex");
        int startOffset = hl.getInt("startOffset");
        int endIndex = hl.getInt("endIndex");
        int endOffset = hl.getInt("endOffset");
        int color = hl.getInt("highlightColor");
        if (hl.getString("color") != null) {
            color = getColorByHex(hl.getString("color"));
        }

        String text = hl.getString("text");
        String note = hl.getString("note");

        Highlight hl2 = new Highlight();

        hl2.bookCode = bookCode;
        hl2.code = code;
        hl2.chapterIndex = chapterIndex;
        hl2.startIndex = startIndex;
        hl2.startOffset = startOffset;
        hl2.endIndex = endIndex;
        hl2.endOffset = endOffset;
        hl2.text = text;
        hl2.note = note;
        hl2.color = color;

        return hl2;
    }

    void onHighlight(Highlight highlight, String commandId) {
        int bookCode = highlight.bookCode;
        int code = highlight.code;
        int chapterIndex = highlight.chapterIndex;
        int startIndex = highlight.startIndex;
        int startOffset = highlight.startOffset;
        int endIndex = highlight.endIndex;
        int endOffset = highlight.endOffset;
        int color = highlight.color;
        String text = highlight.text;
        String note = highlight.note;


        Highlight hl = currentHighlight != null ? currentHighlight :
                new Highlight();

        hl.bookCode = bookCode;
        hl.code = code;
        hl.chapterIndex = chapterIndex;
        hl.startIndex = startIndex;
        hl.startOffset = startOffset;
        hl.endIndex = endIndex;
        hl.endOffset = endOffset;
        hl.text = text;
        hl.note = note;

        switch (commandId) {
            case "deleteHighlight":
                eV.deleteHighlight(hl);
                break;
            case "updateHighlight":
                eV.changeHighlightColor(hl, color);
                break;
        }

    }

    String getColorByInt(int color) {
        String hex = "";
        if (color == -1386018) {
            hex = "#ead9de";    // RED
        } else if (color == -531250) {
            hex = "#f7e4ce"; // YELLOW
        } else if (color == -3679238) {
            hex = "#c7dbfa"; // BLUE
        }

        return hex;
    }

    int getColorByHex(String hex1) {
        int r = Integer.valueOf(hex1.substring(1, 3), 16);
        int g = Integer.valueOf(hex1.substring(3, 5), 16);
        int b = Integer.valueOf(hex1.substring(5, 7), 16);
        return Color.argb(255, r, g, b);
    }


    int getColorByIndex(int colorIndex) {
        int color;
        if (colorIndex == 0) {
            color = Color.argb(255, 238, 230, 142);    // YELLOW
        } else if (colorIndex == 1) {
            color = Color.argb(255, 218, 244, 160); // GREEN
        } else if (colorIndex == 2) {
            color = Color.argb(255, 172, 201, 246); // BLUE
        } else if (colorIndex == 3) {
            color = Color.argb(255, 249, 182, 214); // RED (PINK)
        } else {
            color = Color.argb(255, 249, 182, 214);
        }


        return color;
    }

    @ReactProp(name = "book")
    public void setBook(RelativeLayout ePubView, @Nullable ReadableMap value) {
        try {
            assert value != null;
            BookPath = value.getString("url");
            int id = value.getInt("id");
            eV.bookCode = id;
            this.copyFontToPath(BookPath);
            eV.setBookPath(value.getString("url"));
            eV.reload();

            ePubView.invalidate();
        } catch (ObjectAlreadyConsumedException err) {
            Log.e("ObjAlreadyConsumed", err.getLocalizedMessage(), err);
        }
    }


    public String getStorageDirectory() {
        String res = "";
        //  All book related data will be stored /data/data/com....../files/appName/
        res = thisThem.getFilesDir().getAbsolutePath() + "/" + thisThem.getString(thisThem.getApplicationInfo().labelRes);
        return res;
    }


    public void makeDirectory(String dirName) {
        boolean res;
        String filePath = new String(this.getStorageDirectory() + "/" + dirName);
        File file = new File(filePath);
        if (!file.exists()) {
            res = file.mkdirs();
        } else {
            res = false;
        }
    }

    public void copyBookFromAssetsToDevice(String fileName) {
        try {
            String path = this.getStorageDirectory() + "/books/" + fileName;
            File file = new File(path);
            if (file.exists()) return;
            Context context;
            InputStream localInputStream = thisThem.getAssets().open("books/" + fileName);
            FileOutputStream localFileOutputStream = new FileOutputStream(this.getStorageDirectory() + "/books/" + fileName);

            byte[] arrayOfByte = new byte[1024];
            int offset;
            while ((offset = localInputStream.read(arrayOfByte)) > 0) {
                localFileOutputStream.write(arrayOfByte, 0, offset);
            }
            localFileOutputStream.close();
            localInputStream.close();
            Log.d("AALA", fileName + " copied to phone");
        } catch (IOException localIOException) {
            localIOException.printStackTrace();
            Log.d("aala", "failed to copy");
        }
    }

    public void copyFontToPath(String bookPath) {
        File bPath = new File(bookPath);
        String fontsPath = bPath.getParentFile() + "/fonts";


        File fp = new File(fontsPath);

        if (!fp.exists()) {
            fp.mkdirs();
        }

        int len = customFonts.size();
        for (int i = 0; i < len; i++) {
            CustomFont customFont = customFonts.get(i);
            try {

                String path = fontsPath + "/" + customFont.fontFileName;
                File file = new File(path);
                if (file.exists()) return;


                InputStream localInputStream = thisThem.getAssets().open("fonts/" + customFont.fontFileName);
                FileOutputStream localFileOutputStream = new
                        FileOutputStream(fontsPath + "/" + customFont.fontFileName);
                byte[] arrayOfByte = new byte[1024];

                int offset;
                while ((offset = localInputStream.read(arrayOfByte)) > 0) {
                    localFileOutputStream.write(arrayOfByte, 0, offset);
                }
                localFileOutputStream.close();
                localInputStream.close();
            } catch (IOException localIOException) {
                localIOException.printStackTrace();
            }
        }

    }

    private boolean isSetup() {
        SharedPreferences pref = thisThem.getSharedPreferences("SkyMiniJ", 0);
        return pref.getBoolean("isSetup", false);
    }

    void setup() {
        try {
            if (isSetup()) return;
            this.makeDirectory("books");
            this.makeDirectory("books/fonts");

            this.copyBookFromAssetsToDevice("Alice.epub");

            SharedPreferences pref = thisThem.getSharedPreferences("SkyMiniJ", 0);
            SharedPreferences.Editor edit = pref.edit();
            edit.putBoolean("isSetup", true);
            edit.apply();
        } catch (Exception ignored) {

        }
    }

    @SuppressLint("InlinedApi")
    public static void makeFullscreen(Activity activity) {
        if (Build.VERSION.SDK_INT >= 19) {
            activity.getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_IMMERSIVE
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
        } else if (Build.VERSION.SDK_INT >= 11) {
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LOW_PROFILE);
        }
    }

    public void sendPageLoadingStatus(Boolean isLoading) {
        WritableMap paramsOnPagingStarted = Arguments.createMap();
        WritableMap writableMap = Arguments.createMap();

        paramsOnPagingStarted.putBoolean("isLoading", isLoading);
        writableMap.putMap("nativeEvent", paramsOnPagingStarted);
        thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit("onPagingStatus", writableMap);
    }

    private class PagingDelegate implements PagingListener {

        @Override
        public void onPagingStarted(int i) {
            Log.e("onPagingStarted", "onPagingStarted");
            sendPageLoadingStatus(true);
        }

        @Override
        public void onPaged(PagingInformation pagingInformation) {
            Log.e("onPaged", "onPaged");
            try {
                if (isFirstTime) {
                    isFirstTime = false;

                    NavPoints nps = eV.getNavPoints();
                    chaptersList = Arguments.createArray();
                    WritableMap lastChapter = Arguments.createMap();
                    lastChapter.putInt("chapterIndex", -1);
                    for (int i = 0; i < nps.getSize(); i++) {
                        try {
                            NavPoint np = nps.getNavPoint(i);
                            WritableMap singleChapter = Arguments.createMap();
                            singleChapter.putInt("index", np.index);
                            singleChapter.putInt("chapterIndex", np.chapterIndex);
                            singleChapter.putInt("numberOfPages", eV.getNumberOfPagesInChapter(i));
                            singleChapter.putString("title", np.text);

                            if (lastChapter.getInt("chapterIndex") != singleChapter.getInt("chapterIndex")) {
                                chaptersList.pushMap(singleChapter);
                            }
                            lastChapter = singleChapter;

                        } catch (ObjectAlreadyConsumedException err) {
                            Log.e("ObjAlreadyConsumed", err.getLocalizedMessage(), err);
                        }
                    }

                    WritableMap paramsOnLoad = Arguments.createMap();
                    WritableMap mapOnLoad = Arguments.createMap();
                    paramsOnLoad.putArray("chapters", chaptersList);
                    mapOnLoad.putMap("nativeEvent", paramsOnLoad);
                    thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                            .emit("onChapterLoaded", mapOnLoad);
                }
            } catch (Exception err) {
                Log.e("PagingFinished", err.getLocalizedMessage(), err);
            }
            sendPageLoadingStatus(false);
        }

        @Override
        public void onPagingFinished(int bookCode) {
        }

        @Override
        public int getNumberOfPagesForPagingInformation(PagingInformation pagingInformation) {
            Log.e("getNoOfPagesForPagi", "getNumberOfPagesForPagingInformation");
            PagingInformation pgi = pagingInformationList.get(pagingInformation.chapterIndex);
            if (pgi == null) return 0;

            return pgi.numberOfPagesInChapter;
        }

        @Override
        public void onScanStarted(int bookCode) {
            Log.d("onScanStarted", "Scan process is started");
        }

        /**
         * called when each chapter is scanned
         */
        @Override
        public void onScanned(ItemRef itemRef) {
            Log.d("onScanned", "chapter " + itemRef.chapterIndex + " is scanned.");


        }

        /**
         * called when scan process is finished
         */
        @Override
        public void onScanFinished(int bookCode) {
            Log.d("ScanFinished", "Scan process is finished");
        }


        @Override
        public void onTextExtracted(int i, int i1, String s) {

        }

        @Override
        public String getText(int i, int i1) {
            return null;
        }

        @Override
        public ArrayList getAnyPagingInformations(int i, int i1) {
            Log.e("getAnyPagingInform", "getAnyPagingInformations");
            return pagingInformationList.size() > 0 ? pagingInformationList : null;
        }

        @Override
        public PagingInformation getPagingInformation(PagingInformation pagingInformation) {
            Log.e("getPagingInformation", "getPagingInformation");
            PagingInformation pgi = pagingInformationList.get(pagingInformation.chapterIndex);
            return pgi;
        }
    }


    private class OnClickDelegate implements ClickListener {
        @Override
        public void onClick(int i, int i1) {
            try {
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onTap", true);

//                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
//                        .emit("onSelectTextCanceled", true);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }
        }

        @Override
        public void onImageClicked(int i, int i1, String s) {

        }

        @Override
        public void onLinkClicked(int i, int i1, String s) {

        }

        @Override
        public void onLinkForLinearNoClicked(int i, int i1, String s) {

        }

        @Override
        public boolean ignoreLink(int i, int i1, String s) {
            return false;
        }

        @Override
        public void onIFrameClicked(int i, int i1, String s) {

        }

        @Override
        public void onVideoClicked(int i, int i1, String s) {

        }

        @Override
        public void onAudioClicked(int i, int i1, String s) {

        }

    }

    private class OnPageMovedDelegate implements PageMovedListener {
        @Override
        public void onPageMoved(PageInformation pageInformation) {
            currentPage = pageInformation;
            onPageChange(pageInformation);
            thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("onSelectTextCanceled", true);
            sendHighlightsInPage();
        }


        public void onChapterLoaded(int i) {
            Log.d("CHECKING", String.valueOf(eV.getNumberOfPagesInBook()));
        }

        @Override
        public void onFailedToMove(boolean b) {

        }
    }

    private void onPageChange(@NonNull PageInformation pageInformation) {
        WritableMap map = Arguments.createMap();
        WritableMap params = Arguments.createMap();
        params.putDouble("pageIndexInBook", eV.getPageIndexInBook());
        params.putDouble("pagePositionInBook", pageInformation.pagePositionInBook);
        params.putDouble("pagePositionInChapter", pageInformation.pagePositionInChapter);
        params.putInt("chapterIndex", pageInformation.chapterIndex);
        params.putInt("bookCode", pageInformation.bookCode);
        params.putInt("pageIndex", pageInformation.pageIndex);
        params.putInt("numberOfPagesInChapter", pageInformation.numberOfPagesInChapter);
        params.putString("pageDescription", pageInformation.pageDescription);
        params.putString("chapterTitle", pageInformation.chapterTitle);
        map.putMap("nativeEvent", params);

        Log.d("Test", String.valueOf(pageInformation.numberOfPagesInBook));

        try {
            thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("onPageChange", map);

        } catch (Exception e) {
            Log.e("ReactNative", "Caught Exception: " + e.getMessage());
        }

    }

    void sendHighlightsInPage() {
        WritableMap highlights = Arguments.createMap();
        PageInformation pi = eV.getPageInformation();
        Highlights allHighlights = pi.highlightsInPage;
        if (allHighlights != null) {
            for (int i = 0; i < allHighlights.getSize(); i++) {
                highlights.putMap(String.valueOf(i), getDataFromHighlight(pi.highlightsInPage.getHighlight(i), null, null, false));
            }

            WritableMap map = Arguments.createMap();
            WritableMap params = Arguments.createMap();
            params.putMap("highlights", highlights);
            map.putMap("nativeEvent", params);

            try {
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onHighlightsInPage", map);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }
        }
    }

    int getIndexByColor(int color) {
        int index;
        if (color == Color.argb(255, 238, 230, 142)) {
            index = 0;
        } else if (color == Color.argb(255, 218, 244, 160)) {
            index = 1;
        } else if (color == Color.argb(255, 172, 201, 246)) {
            index = 2;
        } else if (color == Color.argb(255, 249, 182, 214)) {
            index = 3;
        } else {
            index = 0;
        }
        return index;
    }

    BitmapDrawable getMarkerForColor(int color) {
        Drawable mr;
        int index = getIndexByColor(color);
        int di = 0;
        switch (index) {
            case 0:
                di = R.drawable.markeryellow;
                break;
            case 1:
                di = R.drawable.markergreen;
                break;
            case 2:
                di = R.drawable.markerblue;
                break;
            case 3:
                di = R.drawable.markerred;
                break;
            default:
                di = R.drawable.markeryellow;
                break;
        }
        mr = thisThem.getResources().getDrawable(di);
        BitmapDrawable marker = (BitmapDrawable) mr;
        return marker;
    }

    /**
     * SKYEPUB HighlightListener
     */
    class HighlightDelegate implements HighlightListener {
        /**
         * called when a highlight is about to be deleted.
         */
        public void onHighlightDeleted(Highlight highlight) {
            WritableMap map = Arguments.createMap();
            Rect startRect = eV.getStartRect(highlight);
            Rect endRect = eV.getEndRect(highlight);

            WritableMap params = getDataFromHighlight(highlight, startRect, endRect, false);
            currentHighlight = highlight;
            map.putMap("nativeEvent", params);

            try {
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onDeletedHighlight", map);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }
            sendHighlightsInPage();
        }

        /**
         * called when a highlight is needed to be inserted.
         */
        public void onHighlightInserted(Highlight highlight) {
            WritableMap map = Arguments.createMap();
            Rect startRect = eV.getStartRect(highlight);
            Rect endRect = eV.getEndRect(highlight);
            WritableMap params = getDataFromHighlight(highlight, startRect, endRect, false);
            map.putMap("nativeEvent", params);

            try {
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onInsertedHighlight", map);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }
            sendHighlightsInPage();
        }

        /**
         * called when touch event is detected on a highlight.
         */
        public void onHighlightHit(Highlight highlight, int x, int y, Rect startRect, Rect endRect) {
            WritableMap map = Arguments.createMap();
            WritableMap params = getDataFromHighlight(highlight, startRect, endRect, false);
            currentHighlight = highlight;
            map.putMap("nativeEvent", params);

            try {
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onHitHighlight", map);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }
            sendHighlightsInPage();

        }

        /**
         * should return all highlights which are in the chapter.
         */
        public Highlights getHighlightsForChapter(int chapterIndex) {
            Highlights highlights = new Highlights();

            for (int i = 0; i < bookHighlights.getSize(); i++) {
                Highlight hl = bookHighlights.getHighlight(i);
                if (hl.chapterIndex == chapterIndex) {
                    highlights.addHighlight(hl);
                }
            }
            return highlights;
        }

        /**
         * called when a highligh is needed to be updated.
         */
        @Override
        public void onHighlightUpdated(Highlight highlight) {
            WritableMap map = Arguments.createMap();
            Rect startRect = eV.getStartRect(highlight);
            Rect endRect = eV.getEndRect(highlight);
            WritableMap params = getDataFromHighlight(highlight, startRect, endRect, false);
            map.putMap("nativeEvent", params);

            try {
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onUpdatedHighlight", map);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }
            sendHighlightsInPage();

        }

        /**
         * should return note icon bitmap image to match the color.
         */
        @Override
        public Bitmap getNoteIconBitmapForColor(int color, int style) {
            return null;
        }

        /**
         * called when note icon is touched.
         */
        @Override
        public void onNoteIconHit(Highlight highlight) {

        }

        @Override
        public Rect getNoteIconRect(int color, int style) {

            return new Rect();
        }

        /**
         * called when a highlight is needed to be drawn.  this is called when rv.setCustomDrawHighlight(boolean ); is set to true.
         */
        @Override
        public void onDrawHighlightRect(Canvas canvas, Highlight highlight,
                                        Rect highlightRect) {
            Log.w("SKYEPUB", "onDrawHighlightRect is called for Rect " + highlightRect.left + ":" + highlightRect.top + ":" + highlightRect.right + ":" + highlightRect.bottom);
            if (!highlight.isTemporary) {
                if (eV.forceDrawingOnFront) {
                    Paint paint = new Paint();
                    paint.setColor(highlight.color);
                    paint.setStyle(Paint.Style.FILL);
                    paint.setAlpha(110);
                    canvas.drawRect(highlightRect, paint);
                } else {
                    BitmapDrawable marker = getMarkerForColor(highlight.color);
                    marker.setBounds(highlightRect);
                    marker.draw(canvas);
                }
            } else {
                BitmapDrawable marker = getMarkerForColor(highlight.color);
                marker.setBounds(new Rect(highlightRect.left, highlightRect.bottom - 40, highlightRect.right, highlightRect.bottom));
                if (eV.forceDrawingOnFront) {
                    marker.setAlpha(180);
                }
                marker.draw(canvas);
            }
        }

        /**
         * called when caret for text selecting is about to be drawn.
         */
        @Override
        public void onDrawCaret(Canvas canvas, Caret caret) {
            if (caret == null) return;
            Paint paint = new Paint();
            paint.setColor(Color.parseColor("#146fe1"));
            paint.setStrokeWidth(3);

            int cx = 0;
            if (!eV.isRTL()) {
                if (caret.isFirst) cx = caret.x;
                else cx = caret.x + caret.width;
            } else {
                if (caret.isFirst) cx = caret.x + caret.width;
                else cx = caret.x;
            }

            canvas.drawLine((float) cx, (float) (caret.y - caret.height * .7f), (float) cx, (float) (caret.y + caret.height * .7f), paint);
            if (caret.isFirst) {
                canvas.drawCircle((float) cx, (float) (caret.y - caret.height * .7f), 7.0f, paint);
            } else {
                canvas.drawCircle((float) cx, (float) (caret.y + caret.height * .7f), 7.0f, paint);
            }
        }
    }

    public int dpToPx(int dpValue) {
        float density = thisThem.getResources().getDisplayMetrics().density;
        return Math.round((float) dpValue * density);
    }

    public int getHeight() {
        DisplayMetrics metrics = thisThem.getResources().getDisplayMetrics();
        int height = metrics.heightPixels;
        return height;
    }

    public int getWidth() {
        DisplayMetrics metrics = thisThem.getResources().getDisplayMetrics();
        int width = metrics.widthPixels;
        return width;
    }

    public Rect boxPosition(Rect startRect, Rect endRect) {
        int topMargin = dpToPx(54);
        int bottomMargin = dpToPx(54);
        int boxTop = 0;
        int boxLeft = 0;


        int boxWidth = 240;
        int boxHeight = 54;

        if (startRect.top - topMargin > boxHeight) {
            boxTop = startRect.top - boxHeight - dpToPx(7);
            boxLeft = (startRect.left + startRect.width() / 2 - boxWidth / 2);
        } else if ((this.getHeight() - endRect.bottom) - bottomMargin > boxHeight) { // ????????? ????????? ????????? ?????????.
            boxTop = endRect.bottom + dpToPx(7);
            boxLeft = (endRect.left + endRect.width() / 2 - boxWidth / 2);
        } else {
            boxTop = dpToPx(67);
            boxLeft = (startRect.left + startRect.width() / 2 - boxWidth / 2);
        }

        if (boxLeft + boxWidth > this.getWidth() * .9) {
            boxLeft = (int) (this.getWidth() * .9) - boxWidth;
        } else if (boxLeft < this.getWidth() * .1) {
            boxLeft = (int) (this.getWidth() * .1);
        }


        Rect currentBoxFrame = new Rect();
        currentBoxFrame.left = boxLeft;
        currentBoxFrame.top = boxTop;
        currentBoxFrame.right = boxLeft + boxWidth;
        currentBoxFrame.bottom = boxTop + boxHeight;

        return currentBoxFrame;
    }

    public WritableMap getWritableMap(Rect rect) {
        WritableMap writableMap = Arguments.createMap();
        writableMap.putDouble("left", rect.left);
        writableMap.putDouble("right", rect.right);
        writableMap.putDouble("top", rect.top);
        writableMap.putDouble("bottom", rect.bottom);
        writableMap.putDouble("width", rect.width());
        writableMap.putDouble("x", rect.top);
        writableMap.putDouble("y", rect.left);
        writableMap.putDouble("width", rect.width());

        return writableMap;
    }


    public WritableMap getDataFromHighlight(Highlight highlight, Rect startRect, Rect endRect, Boolean isTemporary) {
        String note = "";
        String text = "";
        Boolean isNote = false;
        WritableMap params = Arguments.createMap();

        if (highlight.note != null && !highlight.note.isEmpty()) {
            note = highlight.note;
            isNote = true;
        }
        if (highlight.text != null) {
            text = highlight.text;
        }


        params.putInt("code", highlight.code);
        params.putInt("bookCode", highlight.bookCode);
        params.putInt("chapterIndex", highlight.chapterIndex);
        params.putInt("startIndex", highlight.startIndex);
        params.putInt("startOffset", highlight.startOffset);
        params.putInt("endOffset", highlight.endOffset);
        params.putInt("endIndex", highlight.endIndex);
        params.putInt("highlightColor", highlight.color);
        params.putString("color", getColorByInt(highlight.color));
        params.putString("text", text);
        params.putString("note", note);
        params.putString("chapterTitle", currentPage.chapterTitle);
        params.putBoolean("isNote", isNote);
        params.putInt("pageIndex", currentPage.pageIndex);
        params.putBoolean("isTemporary", isTemporary);


        WritableMap _startRect = Arguments.createMap();
        WritableMap _endRect = Arguments.createMap();

        if (startRect != null) {
            _startRect = getWritableMap(startRect);
        }
        if (endRect != null) {
            _endRect = getWritableMap(endRect);
        }

        if (startRect != null && endRect != null) {
            Rect box = boxPosition(startRect, endRect);
            WritableMap boxPosition = getWritableMap(box);
            params.putMap("position", boxPosition);

        }

        WritableMap parent = Arguments.createMap();
        parent.putDouble("height", eV.getHeight());
        parent.putDouble("width", eV.getWidth());
        params.putMap("parent", parent);

        params.putMap("startRect", _startRect);
        params.putMap("endRect", _endRect);

        return params;
    }

    /**
     * SKYEPUB SelectionListener
     */
    class SelectionDelegate implements SelectionListener {
        /**
         * called when text selection is started
         *
         * @param highlight highlgith object which contains offset and text
         * @param startRect the first rectangle for selection area
         * @param endRect   the last rectable for selection area.
         */
        public void selectionStarted(Highlight highlight, Rect startRect, Rect endRect) {


        }

        /**
         * called when text selection is changed
         */
        public void selectionChanged(Highlight highlight, Rect startRect, Rect endRect) {

        }

        public int getOSVersion() {
            return Build.VERSION.SDK_INT;
        }

        /**
         * called when text selection is over.
         */
        public void selectionEnded(Highlight highlight, Rect startRect, Rect endRect) {
            WritableMap map = Arguments.createMap();
            WritableMap params = getDataFromHighlight(highlight, startRect, endRect, true);
            currentHighlight = highlight;
            map.putMap("nativeEvent", params);

            try {
                eV.setSwipeEnabled(false);
                thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                        .emit("onSelectText", map);

            } catch (Exception e) {
                Log.e("ReactNative", "Caught Exception: " + e.getMessage());
            }

        }

        // in case user touches up selection bar,custom menu view has to be
        // shown near endX,endY.

        /**
         * called when selection is cancelled
         */
        public void selectionCancelled() {
             eV.setSwipeEnabled(true);
            thisThem.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                    .emit("onSelectTextCanceled", true);

        }
    }


}
