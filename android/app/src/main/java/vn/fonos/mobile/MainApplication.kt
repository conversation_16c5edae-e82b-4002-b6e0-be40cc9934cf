package vn.fonos.mobile

import android.content.res.Configuration
import androidx.multidex.MultiDexApplication
import com.clevertap.android.sdk.ActivityLifecycleCallback
import com.clevertap.react.CleverTapRnAPI
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.ReactHost
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import expo.modules.ApplicationLifecycleDispatcher.onApplicationCreate
import expo.modules.ApplicationLifecycleDispatcher.onConfigurationChanged
import expo.modules.ReactNativeHostWrapper
import vn.fonos.mobile.InAppReview.InAppReviewPackage
import vn.fonos.mobile.PreventScreenCapture.PreventScreenCapturePackage
import vn.fonos.mobile.RNHeadsetDetect.RNHeadsetDetectPackage
import vn.fonos.mobile.RNZendesk.RNZendeskPackage
import vn.fonos.mobile.useragent.RNUserAgentPackage

class MainApplication : MultiDexApplication(), ReactApplication {
    private val TAG = "MainApplication"

    override val reactNativeHost: ReactNativeHost =
        ReactNativeHostWrapper(this, object : DefaultReactNativeHost(
            this
        ) {
            override fun getUseDeveloperSupport(): Boolean {
                return BuildConfig.DEBUG
            }

            override fun getPackages(): List<ReactPackage> {
                val packages: MutableList<ReactPackage> = PackageList(this).packages
                packages.add(RNFirebasePackage())
                packages.add(StorySharePackage())
                packages.add(EpubReaderPackage())
                packages.add(RNUtilsPackage())
                packages.add(RNZendeskPackage())
                packages.add(RNHeadsetDetectPackage())
                packages.add(RNUserAgentPackage())
                packages.add(PreventScreenCapturePackage())
                packages.add(InAppReviewPackage())
                return packages
            }

            override fun getJSMainModuleName(): String = "index"

            override val isNewArchEnabled: Boolean
                get() = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED

            override val isHermesEnabled: Boolean
                get() = BuildConfig.IS_HERMES_ENABLED
        })

    override val reactHost: ReactHost
        get() = ReactNativeHostWrapper.createReactHost(applicationContext, reactNativeHost)

    override fun onCreate() {
        ActivityLifecycleCallback.register(this);
        CleverTapRnAPI.initReactNativeIntegration(this);
        super.onCreate()
        SoLoader.init(this, OpenSourceMergedSoMapping)
        if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
            // If you opted-in for the New Architecture, we load the native entry point for this app.
            load()
        }
        onApplicationCreate(this)
  }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        onConfigurationChanged(this, newConfig)
  }
}