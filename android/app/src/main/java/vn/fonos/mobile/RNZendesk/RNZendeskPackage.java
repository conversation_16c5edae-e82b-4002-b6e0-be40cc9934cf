package vn.fonos.mobile.RNZendesk;


import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.ViewManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class RNZendeskPackage implements ReactPackage {

    @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
        return Collections.emptyList();
    }

    @Override
    public List<NativeModule> createNativeModules(
            ReactApplicationContext reactContext) {
        List<NativeModule> modules = new ArrayList<>();

        RNZendesk module = new RNZendesk(reactContext);

        // WritableMap config = Arguments.createMap();

        // config.putString("appId", BuildConfig.ZENDESK_APP_ID);
        // config.putString("clientId", BuildConfig.ZENDESK_CLIENT_ID);
        // config.putString("zendeskUrl", BuildConfig.ZENDESK_URL);
        // config.putString("accountKey", BuildConfig.ZENDESK_CHAT_ACCOUNT_KEY);

        // module.initialize(config);
        modules.add(module);

        return modules;
    }
}
