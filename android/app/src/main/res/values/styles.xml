<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.EdgeToEdge">
        <!-- Customize your theme here. -->
        <!-- Initial config -->        
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>

        <!-- Edge to edge settings -->
        <item name="enforceNavigationBarContrast">false</item>
     </style>

    <!-- Zendesk SDK theme. -->

    <style name="ZendeskCustomTheme" parent="Theme.MaterialComponents.Light">
        <item name="colorPrimary">#ED7873</item>
        <item name="colorPrimaryDark">#ED7873</item>
        <item name="colorAccent">#f6f6f7</item>

    </style>

    <!-- BootTheme should inherit from Theme.SplashScreen -->
    <style name="BootTheme" parent="Theme.BootSplash.EdgeToEdge">
        <item name="bootSplashBackground">@color/bootsplash_background</item>
        <item name="bootSplashLogo">@drawable/bootsplash_logo</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:statusBarColor">#ffffff</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:navigationBarColor">#ffffff</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="postBootSplashTheme">@style/AppTheme</item>
    </style>

</resources>
