/*=========================================
Resets the styling of all HTML elements to a consistent baseline.
=========================================== */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  margin: 0;
  padding: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
*,
*:before,
*:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.group:before,
.group:after {
  content: '';
  display: table;
}
.group:after {
  clear: both;
}
.group {
  zoom: 1;
}
.hidden {
  display: none;
}

/*=========================================
Default styling for help center articles. Alter this file to customize the styling.
=========================================== */

body {
  font: 1em/1.5em 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #666;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  margin: 30px;
  background-color: #ffffff;
}

a {
  color: #4ba7f2;
  text-decoration: none;
}

a:link {
  -webkit-tap-highlight-color: #c5c5c5;
}

strong {
  font-weight: bold;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.25em;
}

h1 {
  font-size: 1.728em;
}
h2 {
  font-size: 1.44em;
}
h3 {
  font-size: 1.2em;
}
h4 {
  font-size: 1em;
}
h5 {
  font-size: 0.833em;
}
h6 {
  font-size: 0.833em;
}

span {
  font-size: 1em !important;
}

p,
ul,
ol {
  margin-bottom: 40px;
}

li {
  margin-bottom: 10px;
}

ul ul,
ol ol {
  margin-left: 20px;
}

footer {
  display: none;
}

img {
  height: auto;
  width: 100%;
}
