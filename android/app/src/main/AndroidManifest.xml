<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    xmlns:tools="http://schemas.android.com/tools"
>

    <uses-feature android:name="android.hardware.camera" android:required="false"/>
    <uses-feature android:name="android.hardware.camera.front" android:required="false"/>
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="mailto"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL" />
            <data android:scheme="tel" />
        </intent>

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" android:host="zalo.me" />
        </intent>
        <package android:name="com.instagram.android" />
        <package android:name="com.facebook.katana"/>
    </queries>
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" tools:node="remove" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> 
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.huawei.systemmanager.permission.ACCESS_INTERFACE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <application 
        android:name=".MainApplication"
        android:requestLegacyExternalStorage="true"
        android:networkSecurityConfig="@xml/network_security"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:theme="@style/BootTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup"
    >
        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/FB_APP_ID"/>
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/FB_CLIENT_TOKEN"/>
        <meta-data android:name="com.onesignal.suppressLaunchURLs" android:value="true"/>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_onesignal_default" />

        <activity 
            android:exported="true" 
            android:launchMode="singleTask" 
            android:name=".MainActivity" 
            android:label="@string/app_name" 
            android:screenOrientation="portrait"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" 
            tools:node="merge"
            android:windowSoftInputMode="adjustResize"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="@string/FIREBASE_DYNAMIC_LINK" android:scheme="https"/>
                <data android:host="@string/FIREBASE_DYNAMIC_LINK" android:scheme="http"/>
            </intent-filter>

            <!-- Branch URI Scheme -->
            <intent-filter>
                <data android:scheme="@string/APP_SCHEME" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <intent-filter  android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="@string/APPS_FLYER_DOMAIN" />
                <data android:scheme="https" android:host="@string/BRANDED_DOMAIN" />
            </intent-filter>
        </activity>
        <activity android:name="com.facebook.FacebookActivity" android:configChanges=
                "keyboard|keyboardHidden|screenLayout|screenSize|orientation" android:label="@string/app_name"/>
        <activity android:name="com.facebook.CustomTabActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/FB_URL_SCHEME" />
            </intent-filter>
        </activity>

        <uses-library
        android:name="org.apache.http.legacy"
        android:required="false" />

        <!-- A receiver that will receive media buttons. Required on pre-lollipop devices -->
        <receiver android:name="androidx.media.session.MediaButtonReceiver" android:exported="true" tools:node="merge"/>
        <activity android:name="zendesk.support.guide.HelpCenterActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <activity android:name="zendesk.support.guide.ViewArticleActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <activity android:name="zendesk.support.request.RequestActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <activity android:name="zendesk.support.requestlist.RequestListActivity"
            android:theme="@style/ZendeskCustomTheme" />
        <activity android:name="zendesk.messaging.MessagingActivity"
            android:theme="@style/ZendeskCustomTheme" />

        <!-- BEGIN CLEVERTAP -->
        <meta-data
            android:name="CLEVERTAP_ACCOUNT_ID"
            android:value="@string/CLEVERTAP_ACCOUNT_ID"/>
        <meta-data
            android:name="CLEVERTAP_TOKEN"
            android:value="@string/CLEVERTAP_TOKEN"/>
        <meta-data 
            android:name="CLEVERTAP_REGION" 
            android:value="@string/CLEVERTAP_REGION"/>
        <meta-data
            android:name="CLEVERTAP_BACKGROUND_SYNC"
            android:value="1"/>
        <meta-data
            android:name="CLEVERTAP_NOTIFICATION_ICON"
            android:value="ic_logo_icon"/>

        <service
            android:name="com.clevertap.android.sdk.pushnotification.fcm.FcmMessageListenerService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <service
            android:name="com.clevertap.android.sdk.pushnotification.CTNotificationIntentService"    
            android:exported="false">
            <intent-filter>
                <action android:name="com.clevertap.PUSH_EVENT" />
            </intent-filter>
        </service>
        <!-- END CLEVERTAP -->

    </application>

</manifest>
