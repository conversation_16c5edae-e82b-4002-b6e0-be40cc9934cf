## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
#Wed Jan 08 15:48:58 ICT 2025
android.enableJetifier=true
android.useAndroidX=true
hermesEnabled=true
newArchEnabled=false
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.jvmargs=-Xms512M -Xmx4096M -XX\:MaxMetaspaceSize\=1g -Dkotlin.daemon.jvm.options\="-Xmx4096M"
org.gradle.parallel=true
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64
