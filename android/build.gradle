// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 29 // APK Increase Size More than 40MB When minSDK > 27
        compileSdkVersion = 35
        targetSdkVersion = 35
        googlePlayServicesAuthVersion = "20.7.0"
        glideVersion = "4.16.0"

        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
        maven { url "https://maven.google.com" }
        maven { url 'https://plugins.gradle.org/m2/' }
        maven {
            url "https://zendesk.jfrog.io/zendesk/repo"
        }
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")


        classpath 'com.google.gms:google-services:4.4.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath 'com.google.firebase:perf-plugin:1.4.2'
        classpath 'io.sentry:sentry-android-gradle-plugin:3.12.0'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)


allprojects {
    repositories {
        maven { url(reactNativeAndroidDir) }
        google()
        mavenCentral {}
        maven { url 'https://zendesk.jfrog.io/zendesk/repo' }
        maven { url 'https://www.jitpack.io' }
        mavenLocal()

        maven { url 'https://maven.google.com' }
    }
}

apply plugin: "expo-root-project"
apply plugin: "com.facebook.react.rootproject"