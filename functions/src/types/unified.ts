import { Timestamp } from 'firebase-admin/firestore'

// Copied from main app types for functions usage
export type MediaEntityType = 
  | 'book' | 'ebook' | 'english_book' | 'course' | 'book_summary'
  | 'guided_meditations' | 'meditation' | 'music' | 'sleep_story'
  | 'kids_story' | 'podcast'

export interface ProgressData {
  entityId?: number
  position: number
  duration?: number
  totalDuration?: number
  finished: boolean
  lastSync: number
  lastUpdatedAt?: number

  
  // Additional progress fields
  [key: string]: any
}

export interface UnifiedProgressDocument {
  _schemaVersion: number
  _lastUpdatedAt: Timestamp
  _metadata: {
    totalCount: number
    nonLibraryKeys?: string[]
  }
  // Dynamic keys: entity_entityId -> ProgressData
  [key: `${MediaEntityType}_${string}`]: ProgressData
}

export interface LibraryItem {
  entityId: number
  entity: MediaEntityType
  createdAt: Date // timestamp
  archived?: boolean
  purchasedType?: string
  updatedAt?: Date
  lastUpdatedAt: number // timestamp
  _docId?: string // Reserved for legacy library id
}

export interface UnifiedLibraryDocument {
  _schemaVersion: number
  _lastUpdatedAt: Timestamp
  _metadata: {
    totalCount: number
    lastPrunedAt?: Timestamp
    migratedAt?: Timestamp
  }
  // Dynamic keys: entity_entityId -> LibraryItem
  [key: `${MediaEntityType}_${string}`]: LibraryItem
}

export interface LibraryMigrationResult {
  success: boolean
  totalItemsMigrated: number
  itemsByEntity: Record<MediaEntityType, number>
  errors: string[]
  migratedAt: number
}

// For legacy library items during migration
export interface LegacyLibraryItem {
  entityId: string | number
  entity: string
  createdAt?: any // Firestore timestamp or Date
  updatedAt?: any
  archived?: boolean
  isArchived?: boolean // legacy field name
  purchasedType?: string // legacy field name
  purchaseType?: string
  deleted?: boolean
  
  // Additional legacy fields that might exist
  [key: string]: any
}