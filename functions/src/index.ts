/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import { onCall, onRequest } from 'firebase-functions/v2/https'
import { onTaskDispatched } from 'firebase-functions/v2/tasks'
import * as logger from 'firebase-functions/logger'
import { initializeApp } from 'firebase-admin/app'
import {
  DocumentData,
  Firestore,
  QueryDocumentSnapshot,
  getFirestore,
} from 'firebase-admin/firestore'
import { getFunctions } from 'firebase-admin/functions'

// Start writing functions
// https://firebase.google.com/docs/functions/typescript

// export const helloWorld = onRequest((request, response) => {
//   logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });


initializeApp()

export const PublicSchema = {
  Book: 'books',
  BookSummary: 'book-summaries',
  Category: 'categories',
  Course: 'courses',
  Ebook: 'ebooks',
  EngAudioBook: 'english-books',
  GuidedMeditation: 'guided-meditations',
  KidsStories: 'kids-stories',
  Meditation: 'meditations',
  Music: 'musics',
  SleepStory: 'sleep-stories',

  Banner: 'banners',
  Collection: 'collections',
  Topics: 'topics',
  MeditationQuote: 'meditation-quotes',

  Channel: 'channels',
  Episode: 'episodes',
  Podcaster: 'podcasters',
  // Review: "reviews",
  Suggestion: 'suggestions',
}

const reviewCollections = {
  [PublicSchema.Book]: 'data',
  [PublicSchema.Ebook]: 'data',
  [PublicSchema.BookSummary]: 'data',
  [PublicSchema.EngAudioBook]: 'data',
}

const ConfigSchemas = ['app_version_manage', 'campaigns', 'promotion_manage']

// WARNING: This is not a drop in replacement solution and
// it might not work for some edge cases. Test your code!
function chunk<T>(arr: T[], chunkSize = 1, cache: T[][] = []) {
  const tmp = [...arr]
  if (chunkSize <= 0) return cache
  while (tmp.length) cache.push(tmp.splice(0, chunkSize) as T[])
  return cache
}

const queryCollectionByPage = async (
  instance: Firestore,
  collection: string,
  result: any[] = [],
  pageToken?: any,
): Promise<any[]> => {
  let query: any = instance.collection(collection)

  if (pageToken) {
    query = query.startAfter(pageToken)
  }

  const snapshot = await query.limit(1000).get()

  if (snapshot.size === 0 || snapshot.size < 1000) {
    return result
  }

  snapshot.docs.forEach((doc: any) => {
    result.push(doc)
  })

  // Return the last document in the snapshot to use as the next page token
  return queryCollectionByPage(
    instance,
    collection,
    result,
    snapshot.docs[snapshot.docs.length - 1],
  )
}

export const migratePublicData = onRequest(
  { region: 'asia-southeast1', timeoutSeconds: 3600, memory: '2GiB', cpu: 1 },
  async (_, response) => {
    const defaultFirestore = getFirestore()
    const newFirestore = getFirestore('asia-southeast-1')

    for (const collection of Object.values(PublicSchema)) {
      logger.info(`Migrating ${collection}`)

      const data: QueryDocumentSnapshot<DocumentData>[] =
        await queryCollectionByPage(defaultFirestore, collection)

      logger.info(`Migrating ${collection} - ${data.length}`)

      const chunkBatchSize = 500

      const chunkedData = chunk<QueryDocumentSnapshot<DocumentData>>(
        data,
        chunkBatchSize,
      )

      logger.info(`Chunked ${collection} into ${chunkedData.length} batches`)

      await Promise.all(
        chunkedData.map(async chunkData => {
          const batch = newFirestore.batch()
          chunkData.forEach(doc => {
            logger.info(`Migrating ${collection} ${doc.id}`)
            if (doc.id) {
              const newDoc = newFirestore.collection(collection).doc(doc.id)
              batch.set(newDoc, doc.data(), { merge: true })
            }
          })

          await batch.commit()
        }),
      )

      logger.info(`Migrated ${collection}`)

      if (reviewCollections[collection]) {
        const subCollectionData = await defaultFirestore
          .collection(collection)
          .doc('reviews')
          .collection(reviewCollections[collection])
          .get()

        logger.info(
          `Migrating ${collection} - ${reviewCollections[collection]} reviews`,
          subCollectionData.size,
        )

        const subCollectionBatch = newFirestore.batch()

        subCollectionData.forEach(doc => {
          const newDoc = newFirestore
            .collection(collection)
            .doc('reviews')
            .collection(reviewCollections[collection])
            .doc(doc.id)
          subCollectionBatch.set(newDoc, doc.data(), { merge: true })
        })

        await subCollectionBatch.commit()
      }
    }

    const batch = newFirestore.batch()
    for (const collection of ConfigSchemas) {
      const data = await defaultFirestore.collection(collection).get()
      data.forEach(doc => {
        if (doc.id) {
          const newDoc = newFirestore.collection(collection).doc(doc.id)
          batch.set(newDoc, doc.data(), { merge: true })
        }
      })
    }
    await batch.commit()

    logger.info('Migration completed')

    response.send('Migration completed')
  },
)

const migrateUserDataFunction = async (userId: string) => {
  const defaultFirestore = getFirestore()
  const newFirestore = getFirestore('asia-southeast-1')

  const userCollection = defaultFirestore.collection('users').doc(userId)
  const defaultUserCollectionData = await userCollection.get()

  // if (!defaultUserCollectionData.exists) {
  //   await userCollection.set({
  //     migratedDatabase: true,
  //   }, {merge: true});

  //   return `Migration completed - userId ${userId}`;
  // }

  if (
    defaultUserCollectionData.exists &&
    defaultUserCollectionData.get('migratedDatabase')
  ) {
    return `Migration completed - userId ${userId}`
  }

  const allCollections = await userCollection.listCollections()

  for (const collection of allCollections) {
    logger.info(`Migrating ${collection.id} - userId ${userId}`)
    const data = await collection.get()

    const batch = newFirestore.batch()

    data.forEach(doc => {
      const newDoc = newFirestore
        .collection('users')
        .doc(userId)
        .collection(collection.id)
        .doc(doc.id)
      batch.set(newDoc, doc.data(), { merge: true })
    })

    await batch.commit()
  }

  await userCollection.set(
    {
      migratedDatabase: true,
    },
    { merge: true },
  )

  logger.info(`Migration completed - userId ${userId}`)

  return `Migration completed - userId ${userId}`
}

export const migrateUserDataFallback = onTaskDispatched(
  {
    region: 'asia-southeast1',
    retryConfig: {
      maxAttempts: 5,
      minBackoffSeconds: 5,
      maxBackoffSeconds: 30,
    },
  },
  async req => {
    const userId = req.data.userId as string

    if (!userId) {
      logger.warn('Missing userId')
      throw new Error('Missing userId')
    }

    await migrateUserDataFunction(userId)
  },
)

export const migrateUserData = onCall(
  {
    region: 'asia-southeast1',
    timeoutSeconds: 3600,
    memory: '512MiB',
    cpu: 0.5,
    maxInstances: 10,
  },
  async req => {
    try {
      const userId = req.data.userId as string

      if (!userId) {
        logger.warn('Missing userId')
        return null
      }

      const queue = getFunctions().taskQueue(
        'locations/asia-southeast1/functions/migrateUserDataFallback',
      )

      const taskId = Date.now().toString()

      await queue.enqueue(
        { userId },
        {
          scheduleDelaySeconds: 60,
          id: taskId,
        },
      )

      const response = await migrateUserDataFunction(userId)

      try {
        await queue.delete(taskId)
      } catch (error) {
        logger.error(error)
      }
      return response
    } catch (error) {
      logger.error(error)
      return null
    }
  },
)

export const manualMigrateUserData = onRequest(
  {
    region: 'asia-southeast1',
    timeoutSeconds: 3600,
    memory: '512MiB',
    cpu: 0.5,
  },
  async (req, res) => {
    const userId = req.query.userId as string

    if (!userId) {
      logger.warn('Missing userId')
      res.send('Missing userId')
      return
    }

    await migrateUserDataFunction(userId)

    res.send(`Migration completed - userId ${userId}`)
  },
)
export { createReview, updateReview, migrateUpdateReview } from './reviews/functions'

// Clean Unified Migration System (v3 - primary)
export {
  migrateToUnifiedSystem,
  manualMigrateToUnifiedSystem,
} from './library/functions'

// Unified System Sync Functions (real-time updates)
export {
  updateUnifiedProgress,
  onLibraryChange,
  onUserLibraryChange,
  onUserArchiveChange,
} from './library/functions'

// Progress Collection Sync Triggers (automatic sync from legacy collections)
export {
  onUserBookProgressChange,
  onUserEbookProgressChange,
  onUserEnglishBookProgressChange,
  onUserCourseProgressChange,
  onUserBookSummaryProgressChange,
  onUserGuidedMeditationProgressChange,
  onUserSleepStoryProgressChange,
  onUserKidsStoryProgressChange,
  onUserPodcastProgressChange,
  onUserMeditationProgressChange,
  onUserMusicProgressChange,
} from './library/unifiedProgressSync'

// export const updateUserCollection = onDocumentWritten({
//   region: "asia-southeast1",
//   document: "users/{userId}/userlibrary/{docId}",
// }, (event) => {
//   const data = event.data?.after.data();
//   const userId = event.params.userId;
//   const collection = "userlibrary";
//   const docId = event.params.docId;
//   const newFirestore = getFirestore("asia-southeast-1");
//   if (!data) return;

//   return newFirestore
//     .collection("users")
//     .doc(userId)
//     .collection(collection)
//     .doc(docId)
//     .set(data, {merge: true});
// });
