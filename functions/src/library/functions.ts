import { getFirestore } from 'firebase-admin/firestore'
import { onCall, onRequest } from 'firebase-functions/v2/https'
import * as logger from 'firebase-functions/logger'

// Import unified migration functions
import { UnifiedLibraryService } from './unifiedLibraryService'
import { migrateUserProgressToUnified } from './unifiedProgressMigration'

/**
 * Core migration function logic - Direct and clean migration
 * userlibrary collection → unified library document
 * progress collections → unified progress document  
 */
const migrateToUnifiedSystemCore = async (userId: string) => {
    logger.info(`Starting clean unified system migration for user: ${userId}`)

    const results: any = {
        success: true,
        userId,
        steps: {},
        timestamp: new Date().toISOString()
    }

    // Step 1: Migrate library to unified document (userlibrary collection → library/data)
    logger.info(`Step 1: Migrating library from userlibrary collection to unified document for user: ${userId}`)
    try {
        const libraryService = new UnifiedLibraryService()
        const libraryResult = await libraryService.migrateUserLibraryToUnified(userId)
        results.steps.libraryMigration = {
            success: libraryResult.success,
            totalItems: libraryResult.totalItemsMigrated,
            itemsByEntity: libraryResult.itemsByEntity,
            errors: libraryResult.errors
        }
        logger.info(`Library migration completed for user: ${userId} - ${libraryResult.totalItemsMigrated} items migrated`)
    } catch (error) {
        logger.error(`Library migration failed for user ${userId}:`, error)
        results.steps.libraryMigration = {
            success: false,
            error: (error as Error).message
        }
    }

    // Step 2: Migrate progress to unified document (progress collections → progress/data)
    logger.info(`Step 2: Migrating progress from collections to unified document for user: ${userId}`)
    try {
        const progressResult = await migrateUserProgressToUnified(userId)
        results.steps.progressMigration = {
            success: progressResult.success,
            totalMigrated: progressResult.totalMigrated,
            libraryItems: progressResult.libraryItems,
            nonLibraryItems: progressResult.nonLibraryItems,
            prunedItems: progressResult.prunedItems,
            error: progressResult.error
        }
        logger.info(`Progress migration completed for user: ${userId} - ${progressResult.totalMigrated} items migrated`)
    } catch (error) {
        logger.error(`Progress migration failed for user ${userId}:`, error)
        results.steps.progressMigration = {
            success: false,
            error: (error as Error).message
        }
    }

    // Determine overall success
    const hasAnyFailure = Object.values(results.steps).some((step: any) => !step.success)
    results.success = !hasAnyFailure

    // Mark migration as completed in user document
    if (results.success) {
        const firestore = getFirestore('asia-southeast-1')
        await firestore.collection('users').doc(userId).set({
            unifiedSystemMigrationCompleted: true,
            unifiedSystemMigrationCompletedAt: new Date(),
        }, { merge: true })
        logger.info(`Unified system migration completed successfully for user: ${userId}`)
    } else {
        logger.warn(`Unified system migration completed with some failures for user: ${userId}`)
    }

    return results
}

// Create single migration function for mobile to call
export const migrateToUnifiedSystem = onCall(
    {
        region: 'asia-southeast1',
        timeoutSeconds: 3600,
        memory: '512MiB'
    },
    async req => {
        try {
            const userId = req.data.userId as string

            if (!userId) {
                logger.warn('Missing or invalid userId in auth context')
                throw new Error('User must be authenticated')
            }

            return await migrateToUnifiedSystemCore(userId)
        } catch (error) {
            logger.error('Unified system migration error:', error)
            throw new Error(`Unified system migration failed: ${error}`)
        }
    },
)

// Manual HTTP trigger for unified system migration - backup option
export const manualMigrateToUnifiedSystem = onRequest(
    {
        region: 'asia-southeast1',
        timeoutSeconds: 3600,
        memory: '1GiB'
    },
    async (req, res) => {
        try {
            const userId = req.query.userId as string

            if (!userId) {
                logger.warn('Missing userId in query parameters')
                res.status(400).send('Missing userId query parameter')
                return
            }

            const result = await migrateToUnifiedSystemCore(userId)
            res.status(200).json(result)
        } catch (error) {
            logger.error('Manual unified system migration error:', error)
            res.status(500).json({
                success: false,
                error: `Unified system migration failed: ${error}`,
            })
        }
    },
)

// Export the unified sync functions
export { onLibraryChange, updateUnifiedProgress } from './unifiedProgressSync'
export { onUserLibraryChange } from './unifiedLibrarySync'
export { onUserArchiveChange } from './unifiedArchiveSync'