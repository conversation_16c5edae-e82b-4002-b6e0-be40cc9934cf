import { getFirestore, FieldValue } from 'firebase-admin/firestore'
import * as logger from 'firebase-functions/logger'
import { MediaEntityType, ProgressData, UnifiedProgressDocument } from '../types/unified'

const firestore = getFirestore('asia-southeast-1')

// Mapping from old collection names to entity types
const COLLECTION_TO_ENTITY_MAP: Record<string, MediaEntityType> = {
  'userbookprogress': 'book',
  'userebookprogress': 'ebook',
  'userenglishbookprogress': 'english_book',
  'usercourseprogress': 'course',
  'userbooksummaryprogress': 'book_summary',
  'userguidedmeditationprogress': 'guided_meditations',
  'usersleepstoryprogress': 'sleep_story',
  'userkidsstoryprogress': 'kids_story',
  'userpodcastprogress': 'podcast',
  'usermeditationprogress': 'meditation',
  'usermusicprogress': 'music',
}

// Maximum non-library items to keep
const MAX_NON_LIBRARY_ITEMS = 50

interface MigrationResult {
  success: boolean
  totalMigrated: number
  libraryItems: number
  nonLibraryItems: number
  prunedItems: number
  error?: string
}

/**
 * Check if progress migration is needed
 */
export async function checkProgressMigrationStatus(userId: string): Promise<boolean> {
  try {
    const progressDocRef = firestore.doc(`users/${userId}/progress/data`)
    const progressDoc = await progressDocRef.get()
    
    // If document exists with schema version 2, migration is complete
    if (progressDoc.exists) {
      const data = progressDoc.data() as UnifiedProgressDocument
      return data._schemaVersion === 1
    }
    
    return false
  } catch (error) {
    logger.error(`Error checking progress migration status for ${userId}:`, error)
    return false
  }
}

/**
 * Get user's library items from unified library document to determine which progress should always be kept
 * Reads directly from users/{userId}/library/data document
 */
async function getUserLibraryItems(userId: string): Promise<Set<string>> {
  const libraryKeys = new Set<string>()
  
  try {
    logger.info(`Reading library items from unified document for user ${userId}`)
    
    // Read from unified library document
    const libraryDocRef = firestore.doc(`users/${userId}/library/data`)
    const libraryDoc = await libraryDocRef.get()
    
    if (libraryDoc.exists) {
      const data = libraryDoc.data()
      
      if (data) {
        // Extract all library keys (format: entity_entityId)
        Object.keys(data).forEach(key => {
          // Skip metadata and system fields
          if (!key.startsWith('_') && key.includes('_')) {
            libraryKeys.add(key)
          }
        })
      }
    } else {
      // If unified library doesn't exist yet, try to read from userlibrary collection as fallback
      logger.info(`Unified library document not found, falling back to userlibrary collection for user ${userId}`)
      
      const userLibraryCollection = await firestore
        .collection(`users/${userId}/userlibrary`)
        .get()
      
      userLibraryCollection.docs.forEach(doc => {
        const data = doc.data()
        if (data && data.entityId && data.entity && !data.deleted) {
          const compositeKey = `${data.entity}_${data.entityId}`
          libraryKeys.add(compositeKey)
        }
      })
    }
    
    logger.info(`Found ${libraryKeys.size} library items for user ${userId}`)
  } catch (error) {
    logger.error(`Error fetching library items for ${userId}:`, error)
  }
  
  return libraryKeys
}

/**
 * Migrate user's progress to unified document
 */
export async function migrateUserProgressToUnified(userId: string): Promise<MigrationResult> {
  try {
    // Check if already migrated
    const alreadyMigrated = await checkProgressMigrationStatus(userId)
    if (alreadyMigrated) {
      return {
        success: true,
        totalMigrated: 0,
        libraryItems: 0,
        nonLibraryItems: 0,
        prunedItems: 0,
      }
    }
    
    // Get user's library items
    const libraryItems = await getUserLibraryItems(userId)
    
    // Collect all progress data
    const allProgress: Array<{
      key: string
      data: ProgressData
      isLibrary: boolean
    }> = []
    
    // Read from all legacy progress collections
    for (const [collection, entity] of Object.entries(COLLECTION_TO_ENTITY_MAP)) {
      const collectionRef = firestore.collection(`users/${userId}/${collection}`)
      const snapshot = await collectionRef.get()
      
      for (const doc of snapshot.docs) {
        const progress = doc.data()
        const entityId = progress.entityId || doc.id
        const key = `${entity}_${entityId}`
        
        allProgress.push({
          key,
          data: progress as ProgressData,
          isLibrary: libraryItems.has(key)
        })
      }
    }
    
    // Separate library and non-library items
    const libraryProgress = allProgress.filter(p => p.isLibrary)
    const nonLibraryProgress = allProgress.filter(p => !p.isLibrary)
    
    // Sort non-library items by lastUpdatedAt and keep only the most recent 50
    nonLibraryProgress.sort((a, b) => b.data.lastSync - a.data.lastSync)
    const keptNonLibraryProgress = nonLibraryProgress.slice(0, MAX_NON_LIBRARY_ITEMS)
    const prunedCount = nonLibraryProgress.length - keptNonLibraryProgress.length
    
    // Build unified document
    const unifiedDoc: UnifiedProgressDocument = {
      _schemaVersion: 1,
      _lastUpdatedAt: FieldValue.serverTimestamp() as any,
      _metadata: {
        totalCount: libraryProgress.length + keptNonLibraryProgress.length,
        nonLibraryKeys: keptNonLibraryProgress.map(p => p.key),
      }
    }
    
    // Add all library progress
    for (const item of libraryProgress) {
      unifiedDoc[item.key as keyof UnifiedProgressDocument] = item.data as any
    }
    
    // Add kept non-library progress
    for (const item of keptNonLibraryProgress) {
      unifiedDoc[item.key as keyof UnifiedProgressDocument] = item.data as any
    }
    
    // Write unified document
    const progressDocRef = firestore.doc(`users/${userId}/progress/data`)
    await progressDocRef.set(unifiedDoc)
    
    logger.info(`Successfully migrated progress for user ${userId}:`, {
      libraryItems: libraryProgress.length,
      nonLibraryItems: keptNonLibraryProgress.length,
      prunedItems: prunedCount,
      total: libraryProgress.length + keptNonLibraryProgress.length
    })
    
    return {
      success: true,
      totalMigrated: libraryProgress.length + keptNonLibraryProgress.length,
      libraryItems: libraryProgress.length,
      nonLibraryItems: keptNonLibraryProgress.length,
      prunedItems: prunedCount,
    }
    
  } catch (error) {
    logger.error(`Error migrating progress for user ${userId}:`, error)
    return {
      success: false,
      totalMigrated: 0,
      libraryItems: 0,
      nonLibraryItems: 0,
      prunedItems: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Mark progress migration as completed
 */
export async function markProgressMigrationComplete(userId: string): Promise<void> {
  const userDocRef = firestore.doc(`users/${userId}`)
  await userDocRef.set({
    unifiedProgressMigrationCompleted: true,
    unifiedProgressMigrationCompletedAt: FieldValue.serverTimestamp(),
  }, { merge: true })
  
  logger.info(`Marked unified progress migration as completed for user: ${userId}`)
}