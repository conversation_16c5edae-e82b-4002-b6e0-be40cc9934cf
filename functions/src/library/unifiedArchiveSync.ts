import { onDocumentWritten } from 'firebase-functions/v2/firestore'
import { getFirestore } from 'firebase-admin/firestore'
import * as logger from 'firebase-functions/logger'

const firestore = getFirestore('asia-southeast-1')

/**
 * Sync function to handle changes to the userarchive collection
 * This function updates the corresponding userlibrary document with the archived status,
 * which then triggers onUserLibraryChange to sync to the unified library.
 * 
 * This "Controlled Chain" approach ensures data integrity by:
 * 1. Only updating the legacy userlibrary collection 
 * 2. Letting onUserLibraryChange handle the unified library sync with complete data
 * 3. Preventing partial data corruption in the unified document
 */
export const onUserArchiveChange = onDocumentWritten(
  {
    document: 'users/{userId}/userarchive/{archiveId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    const { userId } = event.params
    const after = event.data?.after?.exists ? event.data.after.data() : null
    const before = event.data?.before?.exists ? event.data.before.data() : null
    
    // Handle document deletion - when userarchive item is deleted, we might need to unarchive
    if (!after && before) {
      // Archive document was deleted - this could mean the item should be unarchived
      // However, this is complex because we need to determine if there are other archive records
      // For now, we log this case and do nothing to avoid unintended side effects
      logger.info(`User archive document deleted for user ${userId}, entity: ${before.entity}_${before.entityId}`)
      return
    }
    
    // Only process create/update events with data
    if (!after || !after.entity || after.entityId === undefined) {
      logger.warn(`Archive document missing required data for user ${userId}`, { 
        afterData: after 
      })
      return
    }
    
    const { entity, entityId, deleted } = after
    
    try {
      // Find the corresponding document in the legacy userlibrary collection
      // Note: This requires a composite index on (entity, entityId) in the userlibrary collection
      const userLibraryQuery = firestore
        .collection(`users/${userId}/userlibrary`)
        .where('entity', '==', entity)
        .where('entityId', '==', entityId)
        .limit(1)
      
      const querySnapshot = await userLibraryQuery.get()
      
      if (querySnapshot.empty) {
        // Edge case: Archive record exists but no corresponding library item
        // This could happen if:
        // 1. User archived an item that was later removed from their library
        // 2. Race condition during data migration
        // 3. Data integrity issue
        logger.warn(`No matching userlibrary item found for archive event`, {
          userId,
          entity,
          entityId,
          archiveDeleted: deleted
        })
        return
      }
      
      // Update the legacy userlibrary document with the archived status
      // Note: userarchive.deleted=false means "archived", deleted=true means "restored"
      const legacyDocRef = querySnapshot.docs[0].ref
      const isArchived = deleted === false
      
      await legacyDocRef.update({ 
        archived: isArchived,
        updatedAt: new Date() // Update timestamp to reflect the change
      })
      
      logger.info(`Synced archive status to legacy userlibrary`, {
        userId,
        entity,
        entityId,
        archived: isArchived,
        legacyDocId: legacyDocRef.id
      })
      
    } catch (error) {
      logger.error(`Failed to sync archive change for user ${userId}:`, error, {
        entity,
        entityId,
        deleted
      })
      // Don't re-throw to prevent retries on potentially permanent data issues
      // The system can recover from missed archive syncs, but infinite retries would be costly
    }
  }
)