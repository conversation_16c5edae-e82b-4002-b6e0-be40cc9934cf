import { onCall } from 'firebase-functions/v2/https'
import { onDocumentWritten } from 'firebase-functions/v2/firestore'
import { getFirestore, FieldValue } from 'firebase-admin/firestore'
import * as logger from 'firebase-functions/logger'
import { MediaEntityType, ProgressData } from '../types/unified'

const firestore = getFirestore('asia-southeast-1')
const MAX_NON_LIBRARY_ITEMS = 50

// Progress sync system: automatic triggers for all legacy progress collections

/**
 * Universal progress sync triggers for all progress collections
 * Automatically syncs changes from legacy progress collections to unified progress document
 */
export const onUserBookProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userbookprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'book')
  }
)

export const onUserEbookProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userebookprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'ebook')
  }
)

export const onUserEnglishBookProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userenglishbookprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'english_book')
  }
)

export const onUserCourseProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/usercourseprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'course')
  }
)

export const onUserBookSummaryProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userbooksummaryprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'book_summary')
  }
)

export const onUserGuidedMeditationProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userguidedmeditationprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'guided_meditations')
  }
)

export const onUserSleepStoryProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/usersleepstoryprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'sleep_story')
  }
)

export const onUserKidsStoryProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userkidsstoryprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'kids_story')
  }
)

export const onUserPodcastProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/userpodcastprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'podcast')
  }
)

export const onUserMeditationProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/usermeditationprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'meditation')
  }
)

export const onUserMusicProgressChange = onDocumentWritten(
  {
    document: 'users/{userId}/usermusicprogress/{progressId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    await syncProgressToUnified(event, 'music')
  }
)

/**
 * Core function to sync progress changes to unified document
 */
async function syncProgressToUnified(
  event: any,
  entity: MediaEntityType
): Promise<void> {
  const { userId, progressId } = event.params
  const after = event.data?.after?.exists ? event.data.after.data() : null
  const before = event.data?.before?.exists ? event.data.before.data() : null

  const progressDocRef = firestore.doc(`users/${userId}/progress/data`)

  try {
    if (!after) {
      // Document deleted - remove from unified progress
      if (before) {
        const entityId = before.entityId || progressId
        const progressKey = `${entity}_${entityId}`
        
        await firestore.runTransaction(async (transaction) => {
          const progressDoc = await transaction.get(progressDocRef)
          
          if (progressDoc.exists) {
            const data = progressDoc.data()
            if (data && data[progressKey]) {
              // Remove the progress item
              const updates: any = {
                [progressKey]: FieldValue.delete(),
                '_lastUpdatedAt': FieldValue.serverTimestamp(),
              }
              
              // Update metadata
              if (data._metadata) {
                const updatedMetadata = { ...data._metadata }
                updatedMetadata.totalCount = (updatedMetadata.totalCount || 0) - 1
                
                // Remove from nonLibraryKeys if present
                if (updatedMetadata.nonLibraryKeys && updatedMetadata.nonLibraryKeys.includes(progressKey)) {
                  updatedMetadata.nonLibraryKeys = updatedMetadata.nonLibraryKeys.filter(
                    (key: string) => key !== progressKey
                  )
                }
                
                updates['_metadata'] = updatedMetadata
              }
              
              transaction.update(progressDocRef, updates)
              logger.info(`Removed ${progressKey} from unified progress for user ${userId}`)
            }
          }
        })
      }
      return
    }

    // Document added or updated
    const entityId = after.entityId || progressId
    const progressKey = `${entity}_${entityId}`
    
    // Convert to unified progress format
    const progressData: ProgressData = convertLegacyProgressToUnified(after)
    
    await firestore.runTransaction(async (transaction) => {
      const progressDoc = await transaction.get(progressDocRef)
      
      if (!progressDoc.exists) {
        // Create new unified progress document
        const newDoc = {
          _schemaVersion: 1,
          _lastUpdatedAt: FieldValue.serverTimestamp(),
          _metadata: {
            totalCount: 1,
            nonLibraryKeys: [progressKey],
          },
          [progressKey]: progressData
        }
        
        transaction.set(progressDocRef, newDoc)
        logger.info(`Created unified progress with ${progressKey} for user ${userId}`)
      } else {
        // Update existing document
        const data = progressDoc.data()
        const isNewItem = !data || !data[progressKey]
        
        const updates: any = {
          [progressKey]: progressData,
          '_lastUpdatedAt': FieldValue.serverTimestamp(),
        }
        
        if (isNewItem) {
          // Update metadata properly as nested object
          const existingMetadata = data?._metadata || {}
          const updatedMetadata = { ...existingMetadata }
          
          updatedMetadata.totalCount = (existingMetadata.totalCount || 0) + 1
          
          // Add to nonLibraryKeys initially (will be removed if it's in library)
          const existingNonLibraryKeys = existingMetadata.nonLibraryKeys || []
          if (!existingNonLibraryKeys.includes(progressKey)) {
            updatedMetadata.nonLibraryKeys = [...existingNonLibraryKeys, progressKey]
          }
          
          updates['_metadata'] = updatedMetadata
        }
        
        transaction.set(progressDocRef, updates, { merge: true })
        logger.info(`Updated ${progressKey} in unified progress for user ${userId}`)
      }
    })
    
    // Trigger background pruning (non-blocking)
    checkAndPruneNonLibraryItems(userId).catch(error => {
      logger.warn(`Background pruning failed for ${userId}:`, error)
    })
    
  } catch (error) {
    logger.error(`Failed to sync ${entity} progress for user ${userId}:`, error)
    // Don't throw - we don't want to break the user's progress operation
  }
}

/**
 * Convert legacy progress data to unified format
 */
function convertLegacyProgressToUnified(
  legacyProgress: any,
): ProgressData {
  return {
    ...legacyProgress,
    lastUpdatedAt: Date.now(),
  }
}

/**
 * Callable function for manual progress updates (for testing or special cases)
 */
export const updateUnifiedProgress = onCall(
  {
    region: 'asia-southeast1',
  },
  async (request) => {
    // Verify authentication
    if (!request.auth) {
      throw new Error('User must be authenticated to update progress')
    }
    
    const userId = request.auth.uid
    const { entity, entityId, progressData } = request.data as {
      entity: MediaEntityType
      entityId: string
      progressData: Partial<ProgressData>
    }
    
    // Validate input
    if (!entity || !entityId || !progressData) {
      throw new Error('Missing required fields: entity, entityId, progressData')
    }
    
    const progressKey = `${entity}_${entityId}`
    const progressDocRef = firestore.doc(`users/${userId}/progress/data`)
    
    try {
      // Use atomic update to avoid overwriting other progress records
      const updateData: any = {
        [progressKey]: {
          ...progressData,
          lastUpdatedAt: Date.now(),
        },
        '_lastUpdatedAt': FieldValue.serverTimestamp(),
      }
      
      await progressDocRef.update(updateData)
      
      // Trigger pruning check in background (non-blocking)
      checkAndPruneNonLibraryItems(userId).catch(error => {
        logger.warn(`Background pruning failed for ${userId}:`, error)
      })
      
      return { success: true, key: progressKey }
      
    } catch (error) {
      // If document doesn't exist, create it
      if ((error as any).code === 5) { // NOT_FOUND
        const newDoc = {
          _schemaVersion: 1,
          _lastUpdatedAt: FieldValue.serverTimestamp(),
          _metadata: {
            totalCount: 1,
            nonLibraryKeys: [progressKey],
          },
          [progressKey]: {
            ...progressData,
            lastUpdatedAt: Date.now(),
          }
        }
        
        await progressDocRef.set(newDoc)
        return { success: true, key: progressKey, created: true }
      }
      
      logger.error(`Error updating progress for ${userId}:`, error)
      throw new Error('Failed to update progress')
    }
  }
)

/**
 * Check and prune non-library items if needed
 * This runs asynchronously after progress updates
 */
async function checkAndPruneNonLibraryItems(
  userId: string,
): Promise<void> {
  const progressDocRef = firestore.doc(`users/${userId}/progress/data`)
  
  await firestore.runTransaction(async (transaction) => {
    const progressDoc = await transaction.get(progressDocRef)
    
    if (!progressDoc.exists) {
      return
    }
    
    const data = progressDoc.data()
    if (!data) return
    
    // Get library items for this user
    const libraryKeys = await getUserLibraryKeys(userId)
    
    // Identify non-library progress items
    const nonLibraryItems: Array<{
      key: string
      lastUpdatedAt: number
    }> = []
    
    Object.keys(data).forEach(key => {
      if (key.startsWith('_') || key === 'metadata') return
      
      const item = data[key]
      if (item && typeof item === 'object' && 'lastUpdatedAt' in item) {
        if (!libraryKeys.has(key)) {
          nonLibraryItems.push({
            key,
            lastUpdatedAt: item.lastUpdatedAt || 0
          })
        }
      }
    })
    
    // Check if pruning is needed
    if (nonLibraryItems.length <= MAX_NON_LIBRARY_ITEMS) {
      // Update metadata with current non-library keys
      const existingMetadata = data._metadata || {}
      const updatedMetadata = {
        ...existingMetadata,
        nonLibraryKeys: nonLibraryItems.map(item => item.key),
        totalCount: Object.keys(data).filter(k => !k.startsWith('_')).length
      }
      
      transaction.update(progressDocRef, {
        '_metadata': updatedMetadata
      })
      return
    }
    
    // Sort by lastUpdatedAt (oldest first) and identify items to remove
    nonLibraryItems.sort((a, b) => a.lastUpdatedAt - b.lastUpdatedAt)
    const itemsToRemove = nonLibraryItems.slice(0, nonLibraryItems.length - MAX_NON_LIBRARY_ITEMS)
    
    // Build update object to remove old items
    const existingMetadata = data._metadata || {}
    const updatedMetadata = {
      ...existingMetadata,
      nonLibraryKeys: nonLibraryItems
        .slice(itemsToRemove.length)
        .map(item => item.key),
      totalCount: Object.keys(data).filter(k => !k.startsWith('_')).length - itemsToRemove.length,
      lastPrunedAt: FieldValue.serverTimestamp(),
    }
    
    const updates: any = {
      '_metadata': updatedMetadata,
    }
    
    // Add field deletions
    itemsToRemove.forEach(item => {
      updates[item.key] = FieldValue.delete()
    })
    
    transaction.update(progressDocRef, updates)
    
    logger.info(`Pruned ${itemsToRemove.length} non-library items for user ${userId}`)
  })
}

/**
 * Get user's library keys from unified library document
 */
async function getUserLibraryKeys(userId: string): Promise<Set<string>> {
  const libraryKeys = new Set<string>()
  
  try {
    // Read from unified library document
    const libraryDocRef = firestore.doc(`users/${userId}/library/data`)
    const libraryDoc = await libraryDocRef.get()
    
    if (libraryDoc.exists) {
      const data = libraryDoc.data()
      
      if (data) {
        // Extract all library keys (format: entity_entityId)
        Object.keys(data).forEach(key => {
          // Skip metadata and system fields
          if (!key.startsWith('_') && key.includes('_')) {
            libraryKeys.add(key)
          }
        })
      }
    }
  } catch (error) {
    logger.error(`Error fetching library keys for ${userId}:`, error)
  }
  
  return libraryKeys
}

/**
 * Sync function to update unified progress when unified library document changes
 * This ensures progress is retained when items are added to library
 */
export const onLibraryChange = onDocumentWritten(
  {
    document: 'users/{userId}/library/data',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    const { userId } = event.params
    const after = event.data?.after?.exists ? event.data.after.data() : null
    const before = event.data?.before?.exists ? event.data.before.data() : null
    
    if (!after && !before) return
    
    // Check for newly added library items in unified document
    const newLibraryKeys: string[] = []
    
    if (after) {
      Object.keys(after).forEach(key => {
        // Skip metadata and system fields, look for entity_entityId pattern
        if (!key.startsWith('_') && key.includes('_') && after[key]?.entityId) {
          const wasInBefore = before && before[key]?.entityId === after[key].entityId
          if (!wasInBefore) {
            newLibraryKeys.push(key) // key is already in format: entity_entityId
          }
        }
      })
    }
    
    if (newLibraryKeys.length === 0) return
    
    // Update progress metadata to remove these keys from nonLibraryKeys
    const progressDocRef = firestore.doc(`users/${userId}/progress/data`)
    
    try {
      await firestore.runTransaction(async (transaction) => {
        const progressDoc = await transaction.get(progressDocRef)
        
        if (!progressDoc.exists) return
        
        const data = progressDoc.data()
        if (!data?._metadata?.nonLibraryKeys) return
        
        // Remove newly added library items from nonLibraryKeys
        const updatedNonLibraryKeys = data._metadata.nonLibraryKeys.filter(
          (key: string) => !newLibraryKeys.includes(key)
        )
        
        if (updatedNonLibraryKeys.length !== data._metadata.nonLibraryKeys.length) {
          const updatedMetadata = {
            ...data._metadata,
            nonLibraryKeys: updatedNonLibraryKeys
          }
          
          transaction.update(progressDocRef, {
            '_metadata': updatedMetadata
          })
          
          logger.info(`Updated progress metadata for ${newLibraryKeys.length} new library items for user ${userId}`)
        }
      })
    } catch (error) {
      logger.error(`Error updating progress metadata on library change for user ${userId}:`, error)
    }
  }
)