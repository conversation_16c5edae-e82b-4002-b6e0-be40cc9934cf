import { getFirestore, FieldValue } from 'firebase-admin/firestore'
import * as logger from 'firebase-functions/logger'
import { MediaEntityType, LibraryItem, UnifiedLibraryDocument, LegacyLibraryItem, LibraryMigrationResult } from '../types/unified'

const firestore = getFirestore('asia-southeast-1')

// Content types that will be preserved in library
export const PRESERVED_CONTENT_TYPES: string[] = [
  'book',
  'ebook', 
  'english_book',
  'course',
]

/**
 * Service for managing unified library documents
 * Uses the same pattern as UnifiedProgressService for consistency
 */
export class UnifiedLibraryService {
  
  /**
   * Update a library item in the unified document
   */
  async updateLibraryItem(
    userId: string, 
    entity: MediaEntityType, 
    entityId: string, 
    libraryData: Partial<LibraryItem>,
    isNewItem?: boolean
  ): Promise<void> {
    // Skip non-preserved content types
    if (!PRESERVED_CONTENT_TYPES.includes(entity)) {
      logger.info(`Skipping update for non-preserved content type: ${entity}_${entityId}`)
      return
    }
    
    const libraryKey = `${entity}_${entityId}`
    const libraryDocRef = firestore.doc(`users/${userId}/library/data`)
    
    const updateData: any = {
      [libraryKey]: {
        ...libraryData,
        entityId: parseInt(entityId, 10),
        entity,
        lastUpdatedAt: Date.now(),
      },
      '_lastUpdatedAt': FieldValue.serverTimestamp(),
    }
    
    // Update metadata if adding new item - use atomic increment to avoid race conditions
    if (isNewItem) {
      updateData['_metadata.totalCount'] = FieldValue.increment(1)
    }
    
    await libraryDocRef.set(updateData, { merge: true })
  }
  
  /**
   * Remove a library item from the unified document
   */
  async removeLibraryItem(
    userId: string, 
    entity: MediaEntityType, 
    entityId: string
  ): Promise<void> {
    const libraryKey = `${entity}_${entityId}`
    const libraryDocRef = firestore.doc(`users/${userId}/library/data`)
    
    // Check if document exists before attempting removal
    const existingDoc = await libraryDocRef.get()
    if (!existingDoc.exists) return
    
    const updateData: any = {
      [libraryKey]: FieldValue.delete(),
      '_lastUpdatedAt': FieldValue.serverTimestamp(),
      // Use atomic decrement to avoid race conditions, but ensure it doesn't go below 0
      '_metadata.totalCount': FieldValue.increment(-1)
    }
    
    await libraryDocRef.update(updateData)
  }
  
  /**
   * Get the entire unified library document
   */
  async getLibrary(userId: string): Promise<UnifiedLibraryDocument | null> {
    const libraryDoc = await firestore.doc(`users/${userId}/library/data`).get()
    
    if (!libraryDoc.exists) {
      return null
    }
    
    return libraryDoc.data() as UnifiedLibraryDocument
  }
  
  /**
   * Get library items for a specific entity type
   */
  async getLibraryByEntity(userId: string, entity: MediaEntityType): Promise<LibraryItem[]> {
    // Only return items for preserved content types
    if (!PRESERVED_CONTENT_TYPES.includes(entity)) {
      logger.info(`Requested non-preserved content type: ${entity}`)
      return []
    }
    
    const library = await this.getLibrary(userId)
    if (!library) return []
    
    const items: LibraryItem[] = []
    Object.keys(library).forEach(key => {
      if (key.startsWith(entity + '_') && !key.startsWith('_')) {
        const item = library[key as `${MediaEntityType}_${string}`]
        if (item) {
          items.push(item)
        }
      }
    })
    
    return items
  }
  
  /**
   * Check if an item is in the user's library
   */
  async isInLibrary(userId: string, entity: MediaEntityType, entityId: string): Promise<boolean> {
    const libraryKey = `${entity}_${entityId}`
    const libraryDoc = await firestore.doc(`users/${userId}/library/data`).get()
    
    if (!libraryDoc.exists) return false
    
    const data = libraryDoc.data()
    return !!(data && data[libraryKey])
  }
  
  /**
   * Get all library items as a flat array
   */
  async getAllLibraryItems(userId: string): Promise<LibraryItem[]> {
    const library = await this.getLibrary(userId)
    if (!library) return []
    
    const items: LibraryItem[] = []
    Object.keys(library).forEach(key => {
      if (!key.startsWith('_') && key !== 'metadata') {
        const item = library[key as `${MediaEntityType}_${string}`]
        // Only include preserved content types
        if (item && PRESERVED_CONTENT_TYPES.includes(item.entity)) {
          items.push(item)
        }
      }
    })
    
    return items
  }

  async migrateUserArchiveToUserLibrary(userId: string, allLibraryItems: LegacyLibraryItem[]): Promise<void> {
    const userArchiveCollection = await firestore
      .collection(`users/${userId}/userarchive`)
      .get()

    const userArchiveItems = userArchiveCollection.docs.map(doc => doc.data())


    // userlibrary item would mark as archived when userarchive item is exists and deleted = true
    for (const libraryItem of allLibraryItems) {
      const userArchiveItem = userArchiveItems.find(item => item.entityId === libraryItem.entityId && item.entity === libraryItem.entity && item.deleted)
      libraryItem.archived = !!userArchiveItem
    }

    // batch update userlibrary collection
    const batch = firestore.batch()
    for (const libraryItem of allLibraryItems) {
      const libraryItemRef = firestore.doc(`users/${userId}/userlibrary/${libraryItem._docId}`)
      batch.update(libraryItemRef, { archived: libraryItem.archived })
    }  

    await batch.commit()
  }
  
  /**
   * Migrate user's library from userlibrary collection to unified document
   * Direct migration: userlibrary collection → users/{userId}/library/data
   */
  async migrateUserLibraryToUnified(userId: string): Promise<LibraryMigrationResult> {
    const result: LibraryMigrationResult = {
      success: false,
      totalItemsMigrated: 0,
      itemsByEntity: {} as Record<MediaEntityType, number>,
      errors: [],
      migratedAt: Date.now()
    }
    
    try {
      // Check if already migrated
      const existingLibrary = await this.getLibrary(userId)
      if (existingLibrary?._metadata?.migratedAt) {
        logger.info(`Library already migrated for user ${userId}`)
        result.success = true
        return result
      }

      
      // Read directly from userlibrary collection
      const allLibraryItems = await this.collectAllLibrary(userId)

      // Migrate userarchive collection to userlibrary collection
      await this.migrateUserArchiveToUserLibrary(userId, allLibraryItems)
      
      if (allLibraryItems.length === 0) {
        // Create empty library document
        await this.createEmptyLibraryDocument(userId)
        result.success = true
        return result
      }
      
      // Build unified document - we'll set totalCount after processing items
      const unifiedDoc: any = {
        _schemaVersion: 1,
        _lastUpdatedAt: FieldValue.serverTimestamp(),
        _metadata: {
          migratedAt: FieldValue.serverTimestamp(),
        }
      }
      
      let preservedItemsCount = 0
      
      // Debug log all collected items
      logger.info(`Processing ${allLibraryItems.length} collected library items for user ${userId}:`, {
        entityBreakdown: allLibraryItems.reduce((acc, item) => {
          acc[item.entity] = (acc[item.entity] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        englishBookItems: allLibraryItems.filter(item => 
          item.entity === 'english_book' || item.entityId === 160 || item.entityId === '160'
        ).map(item => ({
          entityId: item.entityId,
          entity: item.entity,
          docId: item._docId
        }))
      })

      // Process each library item
      for (const legacyItem of allLibraryItems) {
        try {
          // Debug log for english_book items specifically
          if (legacyItem.entity === 'english_book' || legacyItem.entityId === 160 || legacyItem.entityId === '160') {
            logger.info(`DEBUG: Processing english_book item:`, {
              entityId: legacyItem.entityId,
              entity: legacyItem.entity,
              isInPreservedTypes: PRESERVED_CONTENT_TYPES.includes(legacyItem.entity),
              preservedTypes: PRESERVED_CONTENT_TYPES
            })
          }
          
          // Skip non-preserved content types
          if (!PRESERVED_CONTENT_TYPES.includes(legacyItem.entity)) {
            logger.info(`Skipping non-preserved content type: ${legacyItem.entity}_${legacyItem.entityId}`)
            continue
          }

          logger.info(`Migrating legacy item: ${legacyItem.entity}_${legacyItem.entityId}`)
          
          const libraryItem = this.convertLegacyToLibraryItem(legacyItem)
          const libraryKey = `${libraryItem.entity}_${libraryItem.entityId}`
          
          unifiedDoc[libraryKey] = libraryItem
          
          // Update counters
          result.totalItemsMigrated++
          preservedItemsCount++
          result.itemsByEntity[libraryItem.entity] = (result.itemsByEntity[libraryItem.entity] || 0) + 1
          
        } catch (error) {
          const errorMsg = `Failed to convert legacy item for user ${userId}: ${error}`
          logger.error(errorMsg)
          result.errors.push(errorMsg)
        }
      }
      
      // Set the correct totalCount based on actually preserved items
      unifiedDoc._metadata.totalCount = preservedItemsCount
      
      // Write unified document
      await firestore.doc(`users/${userId}/library/data`).set(unifiedDoc)
      
      logger.info(`Migrated ${result.totalItemsMigrated} library items for user ${userId} (${allLibraryItems.length - preservedItemsCount} items skipped as non-preserved types)`)
      result.success = true
      
    } catch (error) {
      const errorMsg = `Library migration failed for user ${userId}: ${error}`
      logger.error(errorMsg)
      result.errors.push(errorMsg)
      result.success = false
    }
    
    return result
  }
  
  /**
   * Collect all library items directly from userlibrary collection
   * Simple and direct: no shards, just read from the collection
   */
  private async collectAllLibrary(userId: string): Promise<LegacyLibraryItem[]> {
    const allItems: LegacyLibraryItem[] = []
    const skippedItems: any[] = []
    
    try {
      logger.info(`Reading library items from userlibrary collection for user ${userId}`)
      
      const userLibraryCollection = await firestore
        .collection(`users/${userId}/userlibrary`)
        .get()
      
      userLibraryCollection.docs.forEach(doc => {
        const data = doc.data()
        
        // Debug logging for specific entityId 160 (english_book from screenshot)
        if (data && (data.entityId === 160 || data.entityId === '160')) {
          logger.info(`DEBUG: Found entityId 160 item:`, {
            docId: doc.id,
            entityId: data.entityId,
            entity: data.entity,
            hasEntityId: !!data.entityId,
            hasEntity: !!data.entity,
            entityType: typeof data.entity,
            entityValue: data.entity,
            fullData: data
          })
        }
        
        // Only include items that have entityId AND entity
        if (data && data.entityId && data.entity) {
          allItems.push({
            ...data,
            _docId: doc.id, // Preserve original document ID for reference
          } as unknown as LegacyLibraryItem)
        } else {
          // Track skipped items for debugging
          const skipReason = !data ? 'no_data' : 
                           !data.entityId ? 'missing_entityId' : 
                           !data.entity ? 'missing_entity' : 'unknown'
          
          skippedItems.push({
            docId: doc.id,
            entityId: data?.entityId,
            entity: data?.entity,
            skipReason,
            hasData: !!data
          })
          
          // Log skipped english_book items specifically
          if (data && (data.entityId === 160 || data.entityId === '160' || data.entity === 'english_book')) {
            logger.warn(`SKIPPING english_book or entityId 160 item:`, {
              docId: doc.id,
              entityId: data.entityId,
              entity: data.entity,
              skipReason,
              fullData: data
            })
          }
        }
      })
      
      logger.info(`Found ${allItems.length} valid library items for user ${userId}`)
      if (skippedItems.length > 0) {
        logger.info(`Skipped ${skippedItems.length} items during collection:`, {
          skipReasons: skippedItems.reduce((acc, item) => {
            acc[item.skipReason] = (acc[item.skipReason] || 0) + 1
            return acc
          }, {} as Record<string, number>),
          englishBookItems: skippedItems.filter(item => 
            item.entity === 'english_book' || item.entityId === 160 || item.entityId === '160'
          )
        })
      }
      
    } catch (error) {
      logger.error(`Failed to collect userlibrary for user ${userId}:`, error)
      throw error
    }
    
    return allItems
  }
  
  /**
   * Convert legacy library item to new unified format
   */
  private convertLegacyToLibraryItem(legacyItem: LegacyLibraryItem): LibraryItem {
    const entityId = parseInt(String(legacyItem.entityId), 10)
    
    return {
      ...legacyItem,
      entityId,
      entity: legacyItem.entity as MediaEntityType,
      createdAt: this.parseTimestamp(legacyItem.createdAt),
      updatedAt: this.parseTimestamp(legacyItem.updatedAt),
      archived: legacyItem.archived || legacyItem.isArchived || false,
      purchasedType: this.normalizePurchasedType(legacyItem.purchasedType || legacyItem.purchaseType),
      lastUpdatedAt: Date.now(),
    }
  }
  
  /**
   * Parse Firestore timestamp to Date - exported for use in sync functions
   */
  parseTimestamp(timestamp: any): Date {
    if (!timestamp) return new Date()


    if (timestamp instanceof Date) {
      return timestamp
    }
    
    if (timestamp.toDate) {
      return timestamp.toDate()
    }
    
    if (timestamp._seconds !== undefined && timestamp._nanoseconds !== undefined) {
      return new Date(timestamp._seconds * 1000)
    }

    return new Date()
  }
  
  /**
   * Normalize purchase type values - exported for use in sync functions
   */
  normalizePurchasedType(type?: string) {
    if (!type) return 'unknown'
    
    return type.toLowerCase()
  }
  
  /**
   * Create empty library document for new users
   */
  private async createEmptyLibraryDocument(userId: string): Promise<void> {
    const emptyDoc = {
      _schemaVersion: 1,
      _lastUpdatedAt: FieldValue.serverTimestamp(),
      _metadata: {
        totalCount: 0,
        migratedAt: FieldValue.serverTimestamp(),
      }
    }
    
    await firestore.doc(`users/${userId}/library/data`).set(emptyDoc)
  }
}

// Export singleton instance
export const unifiedLibraryService = new UnifiedLibraryService()

// Export helper functions for use in sync functions
export const parseTimestamp = (timestamp: any): Date => unifiedLibraryService.parseTimestamp(timestamp)
export const normalizePurchasedType = (type?: string) => unifiedLibraryService.normalizePurchasedType(type)