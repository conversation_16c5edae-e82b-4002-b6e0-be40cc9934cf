import { onDocumentWritten } from 'firebase-functions/v2/firestore'
import * as logger from 'firebase-functions/logger'
import { MediaEntityType, LibraryItem } from '../types/unified'
import { UnifiedLibraryService, PRESERVED_CONTENT_TYPES, parseTimestamp, normalizePurchasedType } from './unifiedLibraryService'

const unifiedLibraryService = new UnifiedLibraryService()

/**
 * Sync function to handle changes to the legacy userlibrary collection
 * This triggers unified library updates when legacy data changes
 */
export const onUserLibraryChange = onDocumentWritten(
  {
    document: 'users/{userId}/userlibrary/{libraryId}',
    region: 'asia-southeast1',
    database: 'asia-southeast-1',
  },
  async (event) => {
    const { userId } = event.params
    const after = event.data?.after?.exists ? event.data.after.data() : null
    const before = event.data?.before?.exists ? event.data.before.data() : null
    
    try {
      if (!after) {
        // Document deleted
        if (before) {
          const entity = before.entity as MediaEntityType
          const entityId = String(before.entityId)
          
          await unifiedLibraryService.removeLibraryItem(userId, entity, entityId)
          logger.info(`Removed ${entity}_${entityId} from unified library for user ${userId}`)
        }
        return
      }
      
      // Skip soft-deleted items
      if (after.deleted === true) {
        logger.info(`Skipping soft-deleted item ${after.entity}_${after.entityId} for user ${userId}`)
        return
      }
      
      // Skip non-preserved content types
      if (!PRESERVED_CONTENT_TYPES.includes(after.entity)) {
        logger.info(`Skipping non-preserved content type: ${after.entity}_${after.entityId} for user ${userId}`)
        return
      }
      
      // Detect if this is a new item (no before state)
      const isNewItem = !before
      
      // Convert legacy library item to unified format
      const libraryItem: Partial<LibraryItem> = {
        ...after,
        entityId: parseInt(String(after.entityId), 10),
        entity: after.entity as MediaEntityType,
        createdAt: parseTimestamp(after.createdAt),
        updatedAt: parseTimestamp(after.updatedAt),
        archived: after.archived || false,
        purchasedType: normalizePurchasedType(after.purchasedType || after.purchaseType),
        _docId: event.data?.after?.id,
      }
      
      // Update unified library
      await unifiedLibraryService.updateLibraryItem(
        userId, 
        after.entity as MediaEntityType, 
        String(after.entityId), 
        libraryItem,
        isNewItem
      )
      
      logger.info(`Updated ${after.entity}_${after.entityId} in unified library for user ${userId}`)
      
    } catch (error) {
      logger.error(`Failed to sync library change for user ${userId}:`, error)
      // Don't throw - we don't want to break the user's library operation
    }
  }
)

// Helper functions are now imported from unifiedLibraryService to avoid duplication