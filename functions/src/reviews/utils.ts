const MINIMUM_WORDS_TO_PRIORITY_SORT = 20
const countWords = (str?: string) => {
  return str?.split(' ').length || 0
}
export const getPrioritySortDate = (review: any) => {
  if (countWords(review.review) >= MINIMUM_WORDS_TO_PRIORITY_SORT)
    return new Date()

  return null
}

export const calculateWeightedScore = (review: any) => {
  let weightedScore = 0

  if (!review?.review) return weightedScore

  // Add the number of likes + 1
  weightedScore += (review.likes?.length || 0) + 1

  // Add the log base 2 of the number of characters in the review
  weightedScore *= Math.log2(review.review?.length || 0)

  return weightedScore
}
