import { FieldValue, getFirestore } from 'firebase-admin/firestore'
import {
  onDocumentCreated,
  onDocumentUpdated,
  onDocumentWritten,
} from 'firebase-functions/v2/firestore'
import { calculateWeightedScore, getPrioritySortDate } from './utils'

const createReview = onDocumentCreated(
  {
    region: 'asia-southeast1',
    document: 'reviews/{docId}',
    database: 'asia-southeast-1'
  },
  event => {
    const data = event.data?.data()
    if (!data || !!data?.weightedScore) return

    const newFirestore = getFirestore('asia-southeast-1')
    const docId = event.params.docId

    const prioritySortDate = getPrioritySortDate(data.review)

    return newFirestore
      .collection('reviews')
      .doc(docId)
      .set(
        {
          ...data,
          _id: docId,
          likes: [],
          totalLikes: 0,
          deltaLikes: 0,
          weightedScore: calculateWeightedScore(data),
          ...(prioritySortDate && { prioritySortDate }),
        },
        { merge: true },
      )
  },
)

const updateReview = onDocumentUpdated(
  {
    region: 'asia-southeast1',
    document: 'reviews/{docId}',
    database: 'asia-southeast-1',
  },
  event => {
    const before = event.data?.before.data()
    const after = event.data?.after.data()
    if (!after) return

    if ((after.likes?.length || 0) === (before?.likes?.length || 0)) return

    const newDeltaLikes =
      (after.likes?.length || 0) - (before?.likes?.length || 0)
    const diffDeltaLikes = (after?.deltaLikes || 0) - before?.deltaLikes || 0

    const isNewVersion =
      typeof after?.deltaLikes === 'number' && newDeltaLikes === diffDeltaLikes

    if (isNewVersion) return

    const newFirestore = getFirestore('asia-southeast-1')
    const docId = event.params.docId

    return newFirestore
      .collection('reviews')
      .doc(docId)
      .set(
        {
          ...after,
          _id: docId,
          totalLikes: after.likes.length,
          weightedScore: calculateWeightedScore(after),
          deltaLikes: FieldValue.increment(newDeltaLikes),
        },
        { merge: true },
      )
  },
)

const migrateUpdateReview = onDocumentWritten({
  region: "asia-southeast1",
  document: "reviews/{docId}",
}, (event) => {
  const data = event.data?.after.data();
  const docId = event.params.docId;
  const newFirestore = getFirestore("asia-southeast-1");
  if (!data) return;

  return newFirestore.collection("reviews").doc(docId).set({
    ...data,
    likes: data.likes?.length ?
      FieldValue.arrayUnion(...data.likes) :
      [],
  }, { merge: true });
});


export { createReview, updateReview, migrateUpdateReview }
