{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20", "yarn": "3.6.4"}, "main": "lib/index.js", "dependencies": {"@google-cloud/functions-framework": "^3.4.2", "firebase-admin": "^13.0.1", "firebase-functions": "^6.3.2"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.29.1", "firebase-functions-test": "^3.3.0", "typescript": "5.0.4"}, "private": true}