#import "RNHeadsetDetect.h"
#import <AVFoundation/AVFoundation.h>

@implementation RNHeadsetDetect
{
  bool hasListeners;
}

RCT_EXPORT_MODULE(RNHeadsetDetect);

// Called when this module's first listener is added.
-(void)startObserving {
  hasListeners = YES;
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(audioHardwareRouteChanged:) name:AVAudioSessionRouteChangeNotification object:nil];
    [self updateConnectedDevice];
}

// Called when this module's last listener is removed, or on dealloc.
-(void)stopObserving {
    hasListeners = NO;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (NSArray<NSString *> *)supportedEvents {
    return @[@"onChange"];
}

- (void)updateConnectedDevice {
  NSMutableArray *devices = [NSMutableArray array];

  AVAudioSessionRouteDescription* route = [[AVAudioSession sharedInstance] currentRoute];
  for (AVAudioSessionPortDescription* desc in [route outputs]) {
      if ([[desc portType] isEqualToString:AVAudioSessionPortBluetoothHFP] ||
          [[desc portType] isEqualToString:AVAudioSessionPortBluetoothA2DP] ||
          [[desc portType] isEqualToString:AVAudioSessionPortBluetoothLE]) {
          [devices addObject:[desc portName]];
      }
  }
  
  if (hasListeners) {
    [self sendEventWithName:@"onChange" body:@{@"devices": devices}];
  }
}

- (void)audioHardwareRouteChanged:(NSNotification *)notification {
    NSNumber *reason = [notification.userInfo objectForKey:AVAudioSessionRouteChangeReasonKey];
    if ([reason unsignedIntegerValue] == AVAudioSessionRouteChangeReasonNewDeviceAvailable ||
        [reason unsignedIntegerValue] == AVAudioSessionRouteChangeReasonOldDeviceUnavailable) {
        [self updateConnectedDevice];
    }
}

@end
