//
//  RNZendesk.swift
//  FonosMobile
//
//  Created by <PERSON> on 10/22/21.
//  Copyright © 2021 Facebook. All rights reserved.
//

import AnswerBotProvidersSDK
import AnswerBotSDK
import ChatProvidersSDK
import ChatSDK
import CommonUISDK
import Foundation
import MessagingSDK
import SupportSDK
import ZendeskCoreSD<PERSON>

func _navigate(viewController: UIViewController) {
  var topController = UIApplication.shared.keyWindow?.rootViewController
  while (topController?.presentedViewController) != nil {
    topController = topController?.presentedViewController
  }

  let navControl = UINavigationController(rootViewController: viewController)
  topController?.present(navControl, animated: true)
}

@objc(RNZendesk)
class RNZendesk: NSObject {

  @objc
  func _onClose() {
    var topController = UIApplication.shared.keyWindow?.rootViewController
    while (topController?.presentedViewController) != nil {
      topController = topController?.presentedViewController
    }
    topController?.dismiss(animated: true, completion: nil)
  }

  @objc
  func setPrimaryColor(_ hexColor: String) {
    CommonTheme.currentTheme.primaryColor = UIColor(hexString: hexColor)
  }

  @objc
  func initialize(_ options: NSDictionary) {
    Zendesk.initialize(
      appId: options["appId"] as! String, clientId: options["clientId"] as! String,
      zendeskUrl: options["zendeskUrl"] as! String)
    Support.initialize(withZendesk: Zendesk.instance)
    Support.instance?.helpCenterLocaleOverride = "vi"
    AnswerBot.initialize(withZendesk: Zendesk.instance, support: Support.instance!)
    Chat.initialize(
      accountKey: options["accountKey"] as! String, appId: (options["chatAppId"] as! String),
      queue: DispatchQueue.main)

  }

  @objc
  func setUserIdentity(_ visitorInfo: NSDictionary) {
    let identity = Identity.createJwt(token: visitorInfo["userId"] as! String)
    Zendesk.instance?.setIdentity(identity)
  }

  @objc
  func setUserAttribute(_ attribute: String, withValue value: String) {
    guard let zendeskInstance = Zendesk.instance else {
      print("Zendesk instance not initialized")
      return
    }

    let _provider = ZDKUserProvider.init(zendesk: zendeskInstance)
    let userField = [attribute: value]

    _provider.setUserFields(userField) { result, error in
      if let error = error {
        print("Zendesk Error: \(error.localizedDescription)")
      } else {
        print("Zendesk operation completed successfully")
      }
    }
  }

  @objc
  func showGuides() {
    let helpCenterUiConfig = HelpCenterUiConfiguration()
    let articleUiConfig = ArticleUiConfiguration()

    helpCenterUiConfig.showContactOptionsOnEmptySearch = false
    helpCenterUiConfig.showContactOptions = false
    articleUiConfig.showContactOptions = false

    let helpCenterUI = HelpCenterUi.buildHelpCenterOverviewUi(withConfigs: [
      helpCenterUiConfig, articleUiConfig,
    ])
    helpCenterUI.navigationItem.setRightBarButton(nil, animated: true)
    _navigate(viewController: helpCenterUI)
  }

  @objc
  func showTicketList() {
    let config = RequestListUIConfiguration()
    config.allowRequestCreation = false

    let ticketListUI = RequestUi.buildRequestList(with: [config])
    ticketListUI.navigationItem.setRightBarButton(nil, animated: true)
    _navigate(viewController: ticketListUI)
  }

  @objc
  func showTicket(_ id: NSString) {
    let ticketUI = RequestUi.buildRequestUi(requestId: id as String)
    ticketUI.navigationItem.setLeftBarButton(nil, animated: true)
    _navigate(viewController: ticketUI)
  }

  @objc
  func showTicketCreation(_ config: NSDictionary) {
    let _config = RequestUiConfiguration()

    let categoryField = CustomField(fieldId: 900_012_369_046, value: "suggestion")

    _config.subject = (config["subject"] != nil) ? config["subject"] as! String : "New ticket"
    _config.tags = (config["tags"] != nil) ? config["tags"] as! [String] : ["unknown"]
    _config.customFields = [categoryField]

    let addTicketUI = RequestUi.buildRequestUi(with: [_config])

    _navigate(viewController: addTicketUI)

  }

  @objc
  func showLiveChat(_ config: NSDictionary) {
    do {
      let messagingConfugration = MessagingConfiguration()
      messagingConfugration.name = "Fonos Help"

      let chatEngine = try ChatEngine.engine()
      let answerBotEngine = try AnswerBotEngine.engine()
      let supportEngine = try SupportEngine.engine()
      let liveChatUI = try Messaging.instance.buildUI(
        engines: [answerBotEngine, chatEngine, supportEngine], configs: [])

      liveChatUI.navigationItem.leftBarButtonItem = UIBarButtonItem(
        title: "Close", style: .plain, target: self, action: Selector("_onClose"))

      _navigate(viewController: liveChatUI)
    } catch let error as NSError {
      print("\(error.localizedDescription)")
    }
  }

  @objc
  func createTicket(
    _ request: NSDictionary, resolver resolve: @escaping RCTPromiseResolveBlock,
    rejecter reject: @escaping RCTPromiseRejectBlock
  ) {
    let _request = ZDKCreateRequest()
    let _provider = ZDKRequestProvider()
    // TODO: Set custom fields.
    _request.subject = request["subject"] as? String
    _request.tags = request["tags"] as? [Any]
    _request.requestDescription = request["description"] as? String

    _provider.createRequest(_request) { result, error in
      if let error = error {
        return reject("ERROR", "CREATE TICKET ERROR", error)
      }
      return resolve("ok")
    }
  }

  @objc
  func registerForNotifications(_ deviceToken: Data) {
    Chat.registerPushToken(deviceToken)
  }

}
