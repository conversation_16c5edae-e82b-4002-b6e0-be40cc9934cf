/*
  Localizable.strings
  FonosMobile

  Created by <PERSON> on 11/18/21.
  Copyright © 2021 Facebook. All rights reserved.
*/

/* Unified */

/* Status label for a failed message, notifying the user to tap the cell to try resend the message. */
"ios.common.ui.message.action.retry.button.label" = "Thử lại";

/* Copy label for UIMenuItem in the editing menu for messages in the thread. Indicates a user can copy the text of the message to their clipboard. */
"ios.common.ui.message.edit_menu.copy.button.label" = "Sao chép";

/* Delete label for UIMenuItem for messages in the thread. Indicates a user can delete a message if it has failed in the thread. */
"ios.common.ui.message.edit_menu.delete.button.label" = "Xóa";

/* Descriptive label describing the state of reconnection to the internet. Current state: Connection failed. Appears after the retry failed to connect. */
"ios.common.ui.reconnection.connection_failed.label" = "Kết nối thất bại";

/* Descriptive label describing the state of reconnection to the internet. Current state: reconnecting. Appears after the retry button is pressed. */
"ios.common.ui.reconnection.reconnecting.label" = "Đang kết nối lại...";

/* Title label, shown in the navigation bar. */
"ios.common.ui.title" = "Liên hệ Fonos";

/* Label for the button used to retry some operations. */
"ios.common.ui.retry.button.label" = "Thử lại";

/* Hint for the input field indicating that the user should enter a message. */
"ios.common.ui.type_a_message.input.label" = "Nhập tin nhắn ...";

/* Label for button on alert which is shown to decline a request. The button dismisses the alert. */
"ios.common.ui.alert.no.button.label" = "Không";

/* Label for button on alert which is shown to accept a request. The button dismisses the alert. */
"ios.common.ui.alert.yes.button.label" = "Có";

/* Label for button on alert which is shown when an image could not be saved. The button dismisses the alert. */
"ios.common.ui.attachment.error.alert.ok.button.label" = "OK";

/* Title for an alert which is shown when an image could not be saved. */
"ios.common.ui.attachment.error.alert.title.label" = "Lưu hình ảnh thất bại";

/* Label for button which cancels attachment selection. */
"ios.common.ui.attachment.selection.cancel.button.label" = "Hủy";

/* Label for the source of selecting an attachment via Google Drive or iCloud. Displays as a button to tap. */
"ios.common.ui.attachment.selection.document.button.label" = "Document";

/* Label for the source of selecting an image attachment from the Camera. Displays as a button to tap. */
"ios.common.ui.attachment.selection.camera.button.label" = "Camera";

/* Label for the source of selecting an image attachment. Displays as a button to tap. */
"ios.common.ui.attachment.selection.photo_library.button.label" = "Photo library";

/* Status label for a failed file upload. Reason: Sending attachments is not supported by backend. */
"ios.common.ui.message.status.file.sending_is_not_supported.label" = "Attachments not supported";

/* Status label for a failed upload showing the attachment file size has been exceeded. With the max file size in brackets eg.(20MB). */
"ios.common.ui.message.status.file.size.limit.label" = "Exceeds max file size (%@)";

/* Status label for a failed file upload. Reason: Sending a file of this type is unsupported. */
"ios.common.ui.message.status.file.type_not_supported.label" = "Attachment type not supported";

/* Answer Bot */

/* Initial greetings from Answer Bot */
"ios.answer_bot.text.label.hi" = "Chào!";
"ios.answer_bot.text.label.ask_a_question" = "Ask me a question and I'll find the answer for you.";

/* Shown after initial greetings if Support, Chat, or other Messaging engines are configured */
"ios.answer_bot.cell.text.inactivity_get_in_touch_message" = "Hoặc bạn có thể liên hệ";

/* Input hint text */
"ios.answer_bot.ui.type_your_question.input.label" = "Câu hỏi của bạn là …";

/* Shown when a single article was found by Answer Bot */
"ios.common.ui.cell.text.single_article" = "Đây là một bài viết có thể giúp ích:";

/* Shown when multiple articles were found by Answer Bot */
"ios.common.ui.cell.text.many_articles" = "Đây là vài bài viết có thể giúp ích:";

/* Shown when no articles are found by Answer Bot */
"ios.common.ui.cell.text.no_articles" = "Tôi không tìm thấy bài viết nào liên quan";
"ios.common.ui.cell.text.get_in_touch" = "Bạn có muốn liên hệ?";
"ios.answer_bot.cell.text.ask_me_another_question" = "Hoặc bạn có câu hỏi khác?";

/* Shown at the bottom of an article, above the yes and no labels */
"ios.answer_bot.article_view.label.question" = "Does this article answer your question?";
"ios.answer_bot.resolution_view.question.text.tell_us_why" = "Was the article related to your question?";

/* Quick reply labels shown to ask whether an article was helpful or not */
"ios.answer_bot.article.view.anwser.label.yes" = "Yes";
"ios.answer_bot.resolution_view.button.label.no" = "No";

/* Shown when the end-user marks a question as resolved */
"ios.answer_bot.cell.text.question_resolved" = "Nice. Knowledge is power.";

/* Shown after a successful resolution, asking if there's another question */
"ios.answer_bot.cell.text.type_another_question" = "If there's anything else I can find for you, just type another question.";

/* Shown after an unsuccessful resolution */
"ios.answer_bot.cell.text.question_unresolved" = "I see. Your question is still unresolved.";

/* Shown if the user navigates back to the conversation and they didn't select yes / no on the previous article screen */
"ios.answer_bot.cell.text.article_helpful_question" = "Did the article you viewed help to answer your question?";
"ios.answer_bot.resolution_view.question.text.tell_us_more" = "Please tell us more. Was the article related to your question?";

/* Shown when there is no contact engine like Support or Chat */
"ios.answer_bot.cell.text.ask_me_another_question_no_transfer_options" = "You can try asking me another question.";

/* Labels shown when an end-user can transfer to Support, Chat, and other contact engines */
"ios.conversation.ui.chat.handover.message.selection" = "Gặp nhân viên Chăm sóc khách hàng";
"ios.ZDKRequests.createRequest.leaveAMessage.title" = "Để lại tin nhắn";

/* Errors */
"ios.answer_bot.load_article.error" = "Failed to load article";
"ios.answer_bot.text.label.disabled" = "Uh-oh. Sorry, something’s up. I can’t answer questions right now.";

/* Labels shown after the end-user selects the "Leave a message" option */
"ios.SupportEngine.requestCreated.conversationsEnabledMessage" = "Cảm ơn bạn. Tin nhắn của bạn đã được gửi đến bộ phận CSKH. Bạn có thể xem thông báo này và bổ sung thêm chi tiết tại đây:";
"ios.SupportEngine.requestCreated.conversationsOff.message" = "Cảm ơn bạn. Tin nhắn của bạn đã được gửi đến bộ phận CSKH. Họ sẽ liên hệ với bạn qua email.";
"ios.SupportEngine.requestCreated.requestList.button" = "Xem tin nhắn";


/* Chat */

// MARK: Engine handover
/* Label for button that when tapped on will allow the user to switch to chatting with an agent. */
"ios.conversation.ui.chat.handover.message.selection" = "Trò chuyện với bộ phận CSKH";

/* Button action acknowledgement to let the user know they will be connected to an agent */
"ios.conversation.ui.chat.welcome_message.handover" = "OK. Sẽ có nhân viên CSKH liên hệ bạn ngay!";

// MARK: Customer satisfaction (CSAT)

/* CSAT message shown to the user when an agent has requested the user to rate their chat experience. */
"ios.conversation.ui.chat.rating.message" = "Hey, just checking in to see how your chat with %@ has been going?";

/* CSAT button label that the user presses to give a negative chat rating. If the emojis don’t translate well to your language, please remove them. */
"ios.conversation.ui.chat.rating.button_label.bad" = "Bad :thumbs-down-emoji:";

/* CSAT button label that the user presses to give a positive chat rating. If the emojis don’t translate well to your language, please remove them. */
"ios.conversation.ui.chat.rating.button_label.good" = "Good :thumbs-up-emoji:";

/* CSAT message shown to the user inidicating that they have the option to leave a comment about the chat. */
"ios.conversation.ui.chat.rating.comment.prompt.message" = "We value your feedback. If you like, you can also add a comment with more detail.";

/* CSAT button label that the user presses if they would like to leave a comment about their chat experience. */
"ios.conversation.ui.chat.rating.comment.action.button_label" = "Add a comment";

/* Input field placeholder suggesting the user to enter a comment about how the chat is going. Should be kept short to be visible within the input */
"ios.conversation.ui.chat.rating.add.comment.composer.hint" = "Type your comment...";

/* CSAT confirmation message. Shown to the user to let them know that their comment has been received. 'chat' is a noun and must be translated. */
"ios.conversation.ui.chat.rating.acknowledgement.message" = "Thank you for the feedback. You can now get back to your chat.";

// MARK: Email transcript UIAlertAction flow

/* Email transcript UIAlertAction title, shown in the chat menu. User clicks it when they want to receive a chat transcript. */
"ios.conversation.ui.chat.transcript.prompt.request_transcript.title" = "Request transcript";

/* Email transcript UIAlertAction prompt message. Prompts user if they'd like to send the transcript to their email. */
"ios.conversation.ui.chat.transcript.prompt.request_transcript_prompt.label" = "Would you like a transcript of this chat sent to your email?";

/* Email transcript UIAlertAction message to prompt the user to enter their email. */
"ios.conversation.ui.chat.transcript.prompt.ask_email.label" = "What email address should the transcript be sent to?";

/* Email transcript UIAlertAction text field placeholder for the the email address field. */
"ios.conversation.ui.chat.transcript.prompt.email_placeholder" = "Email address.";

/* Email transcript UIAlertAction prompt message to inform user they entered a wrong email, and should try enter a new one. */
"ios.conversation.ui.chat.transcript.prompt.invalid_email.message" = "A valid email address is required.";

/* Email transcript UIAlertAction response message for the user to stop the email transcript prompt flow. */
"ios.conversation.ui.chat.transcript.prompt.no" = "No";

/* Email transcript UIAlertAction response message for the user to continue the email transcript prompt flow. */
"ios.conversation.ui.chat.transcript.prompt.yes" = "Yes";

/* Email transcript UIAlertAction label to cancel the action. */
"ios.conversation.ui.chat.transcript.prompt.cancel" = "Cancel";

/* Email transcript UIAlertAction label to confirm the action, and send the email transcript request. */
"ios.conversation.ui.chat.transcript.prompt.send_email" = "Send";

/* Email transcript banner label displayed after user finishes the flow and sets the email they'd like the chat transcript to be sent to. */
"ios.conversation.ui.chat.transcript.email_set.label" = "Transcript will be sent to %@ when the chat ends";

// MARK: End chat flow

/* End chat UIAlertAction title, shown in the chat menu. User clicks it when they want to end an active chat. */
"ios.conversation.ui.end_chat.button_label" = "End chat";

// MARK: System messages

/* System message shown when the user ended the chat, or the chat was ended by timeout. */
"ios.conversation.ui.end_chat.system_message" = "Chat ended";

/* System message when a user joins the chat. Placeholder gets replaced with the display name of the user. */
"ios.conversation.ui.member.join" = "%@ joined the chat";

/* System message when an agent leaves the chat. Placeholder gets replaced with the display name of the agebt. */
"ios.conversation.ui.member.leave" = "%@ left the chat";

/* System message to show the user their position in the queue. */
"ios.conversation.ui.queue.position" = "Queue position: %lu";

// MARK: Chat form field hints

/* Input field placeholder suggesting the user to enter their name. Should be kept short to be visible within the input field. */
"ios.conversation.ui.pre_chat.form.add.name.composer.hint" = "What is your name?";

/* Input field placeholder suggesting the user to enter their email address. Should be kept short to be visible within the input field. */
"ios.conversation.ui.pre_chat.form.add.email.composer.hint" = "What is your email address?";

/* Input field placeholder suggesting the user to enter their phone number. Should be kept short to be visible within the input field. */
"ios.conversation.ui.pre_chat.form.add.phone.number.composer.hint" = "What is your number?";

// MARK: Pre-chat form flow

/* Pre-chat form welcome message shown when a user starts the flow, asking them for their query. */
"ios.conversation.ui.chat.welcome_message.conversation_start" = "Hi. What can we help you with today?";

/* Pre-chat form statement, informing the user more information is required before reaching out to an agent. */
"ios.conversation.ui.pre_chat.form.information.request.message" = "I will find someone to help you. First I need a few pieces of information.";

/* Pre-chat form question prompting the user to enter their name. */
"ios.conversation.ui.pre_chat.form.name.request.message" = "Tên của bạn là gì?";

/* Pre-chat form question prompting the user to enter their email address. */
"ios.conversation.ui.pre_chat.form.email.request.message" = "Email của bạn là gì?";

/* Pre-chat form statement, informing the user they entered an invalid email, and need to try again. */
"ios.conversation.ui.pre_chat.form.email.retry.message" = "The email address you entered is incorrect. Enter a valid email address.";

/* Pre-chat form question prompting the user to enter their phone number. */
"ios.conversation.ui.pre_chat.form.phone.number.request.message" = "Số điện thoại của bạn là gì?";

/* Pre-chat form question prompting the user to pick the department they'd like to speak to. */
"ios.conversation.ui.pre_chat.form.department.choice.message" = "Which department would best answer your question?";

/* Pre-chat form button label that allows the user to skip an optional form question. */
"ios.conversation.ui.pre_chat.form.skip.button_label" = "Bỏ qua";

/* Pre-chat form message to acknowledge a user entering their name. */
"ios.conversation.ui.pre_chat.form.thank_you.message" = "Cảm ơn.";

// MARK: Offline form flow

/* Offline form welcome message informing the user no one is online. This is followed by `offline_form.message_prompt` message asking for more info. */
"ios.conversation.ui.chat.offline_form.welcome_message" = "Hi. Unfortunately nobody is online at the moment.";

/* Offline form statement, informing the user information is needed to get in contact at a later point in time. */
"ios.conversation.ui.chat.offline_form.introduction_message" = "To contact you when someone is available, I need some more information.";

/* Offline form completion message shown to the user when they complete the form. */
"ios.conversation.ui.chat.offline_form.completion_acknowledgement" = "We've received your message and will get back to you as soon as possible.";

/* Offline message informing the user that there is no active agents, and to check back later. Shown when ChatConfiguration.isOfflineFormEnabled is disabled */
"ios.conversation.ui.offline.no_agents_online" = "Unfortunately, nobody is online at the moment. Check back later.";

/* Offline message asking the user for their details, like name and email */
"ios.conversation.ui.chat.offline_form.message__field_prompt" = "What can we help you with when we are back online?";


/* Support */
"ios.ZDKHelpCenter.articleAttachmentsError" = "Tải tệp đính kèm thất bại";
"ios.ZDKHelpCenter.helpCenterOverview.see_all.singletitle" = "Xem tất cả bài viết";
"ios.ZDKHelpCenter.helpCenterOverview.see_all.title" = "Xem tất cả %lu bài viết";
"ios.ZDKHelpCenter.helpCenterOverview.title" = "Hỗ trợ";
"ios.ZDKHelpCenter.noArticles.title" = "Không tìm thấy bài viết nào cả";
"ios.ZDKHelpCenter.search.noResults.title" = "Không có kết quả cho từ khóa ";
"ios.ZDKHelpCenter.support.contactError" = "Không thể kết nối, vui lòng thử lại!";
"ios.ZDKHelpCenter.support.contactSupport" = "Liên hệ Fonos";
"ios.ZDKHelpCenter.toastRetryButton" = "Thử lại";
"ios.ZDKRequests.commentList.error" = "Failed to load comments";
"ios.ZDKRequests.connectionError" = "No connection";
"ios.ZDKRequests.conversations.back.button" = "Back";
"ios.ZDKRequests.createRequest.attachments.confirm" = "OK";
"ios.ZDKRequests.createRequest.attachments.download" = "Downloading";
"ios.ZDKRequests.createRequest.attachments.downloading" = "File attachment is still downloading. Please wait.";
"ios.ZDKRequests.createRequest.cancel.button" = "Hủy";
"ios.ZDKRequests.createRequest.cellError" = "Gửi tin nhắn thất bại. Chạm để thử lại";
"ios.ZDKRequests.createRequest.description.placeholder" = "Nội dung";
"ios.ZDKRequests.createRequest.emailfield.placeholder" = "Email";
"ios.ZDKRequests.createRequest.error.request" = "Gửi yêu cầu thất bại";
"ios.ZDKRequests.createRequest.failed.closed" = "Cuộc trò chuyện này đã đóng.";
"ios.ZDKRequests.createRequest.failed.delete" = "Xóa";
"ios.ZDKRequests.createRequest.failed.retry" = "Thử lại";
"ios.ZDKRequests.createRequest.input.new" = "Nhập tin nhắn...";
"ios.ZDKRequests.createRequest.input.reply" = "Phản hồi...";
"ios.ZDKRequests.createRequest.input.unsent.body" = "Going back will delete your message.\nAre you sure you want to delete it?";
"ios.ZDKRequests.createRequest.input.unsent.title" = "Tin nhắn chưa được gửi";
"ios.ZDKRequests.createRequest.leaveAMessage.title" = "Để lại tin nhắn";
"ios.ZDKRequests.createRequest.message.sent" = "Đã gửi";
"ios.ZDKRequests.createRequest.messages.today" = "Hôm nay";
"ios.ZDKRequests.createRequest.messages.yesterday" = "Hôm qua";
"ios.ZDKRequests.createRequest.name.placeholder" = "Tên";
"ios.ZDKRequests.createRequest.sending" = "Đang gửi...";
"ios.ZDKRequests.createRequest.submit.button" = "Gửi";
"ios.ZDKRequests.createRequest.title" = "Yêu cầu Hỗ trợ";
"ios.ZDKRequests.leftBarButton.title_cancel" = "Hủy";
"ios.ZDKRequests.requestList.cell.closed" = "Yêu cầu đã đóng";
"ios.ZDKRequests.requestList.cell.failed" = "Gửi tin nhắn thất bại";
"ios.ZDKRequests.requestList.cell.me" = "Me";
"ios.ZDKRequests.requestList.cell.responders" = "Re:";
"ios.ZDKRequests.requestList.cell.seperator" = ", ";
"ios.ZDKRequests.requestList.error" = "Tải danh sách yêu cầu thất bại";
"ios.ZDKRequests.requestList.title" = "Hỗ trợ";
"ios.ZDKRequests.requestListEmpty.button" = "Tạo yêu cầu";
"ios.ZDKRequests.requestListEmpty.subtitle" = "Conversations started from this app will be visible here";
"ios.ZDKRequests.rightBarButton.title.contact" = "Liên hệ Fonos";
"ios.sdk.ZDKHelpCenter.article.voting.title" = "Was this article helpful?";
"ios.sdk.ZDKRequests.createRequest.attachment.fileTooBig" = "Max attachment size is %@mb";
"ios.sdk.ZDKRequests.leftBarButton.close.title" = "Close";
"ios.sdk.ZDKRequests.pushNotification.dismiss.title" = "Dismiss";
"ios.sdk.ZDKRequests.pushNotification.open.title" = "Open";
"ios.SupportEngine.greeting.message" = "Type a message and I will find someone who can help you.";
"ios.SupportEngine.messageSend.errorMessage" = "Something went wrong and I wasn't able to send your message. Please try again later.";
"ios.SupportEngine.messageSend.retry.button" = "Retry";
"ios.SupportEngine.requestCreated.conversationsDisabled.message" = "Thank you. A message has been sent to the team. If a reply is needed, they will contact you on %@.";
"ios.SupportEngine.requestCreated.conversationsEnabledMessage" = "Thank you. A message has been sent to the team. You can view this message and add additional details here:";
"ios.SupportEngine.requestCreated.conversationsOff.message" = "Thank you. A message has been sent to the team. If a reply is needed, they will contact you by email.";
"ios.SupportEngine.requestCreated.emailInvalid.message" = "Something isn't quite right with this address. Try re-entering it.";
"ios.SupportEngine.requestCreated.emailPrompt.hint" = "Enter your email...";
"ios.SupportEngine.requestCreated.emailPrompt.message" = "If a reply is needed, someone will contact you by email. What is your email address?";
"ios.SupportEngine.requestCreated.emailValidationFailed.message" = "The email address you entered is incorrect. Enter a valid email address.";
