@objc(FasterImageViewManager)
class FasterImageViewManager: RCTViewManager {
  
  override func view() -> UIView! {
    return FasterImageView()
  }
  
  @objc override static func requiresMainQueueSetup() -> <PERSON><PERSON> {
    return false
  }
}

/// A wrapper around `LazyImageView` to make it compatible with React Native.
@objc(FasterImageView)
class FasterImageView: UIView {
  
  // MARK: - Initializers
  
  init() {
    super.init(frame: .zero)
    addSubview(lazyImageView)
    lazyImageView.translatesAutoresizingMaskIntoConstraints = false
    NSLayoutConstraint.activate([
      lazyImageView.topAnchor.constraint(equalTo: topAnchor),
      lazyImageView.bottomAnchor.constraint(equalTo: bottomAnchor),
      lazyImageView.leadingAnchor.constraint(equalTo: leadingAnchor),
      lazyImageView.trailingAnchor.constraint(equalTo: trailingAnchor),
    ])
    
    lazyImageView.pipeline = .init(configuration: .withDataCache)
    lazyImageView.transition = .none
    lazyImageView.placeholderView = nil
    lazyImageView.onCompletion = { [weak self] result in
      self?.completionHandler(with: result)
    }
  }
  
  required init?(coder: NSCoder) {
    super.init(coder: coder)
  }
  
  // MARK: - Views
  
  private lazy var lazyImageView = LazyImageView()
  
  // MARK: - Callbacks
  
  @objc var onError: RCTDirectEventBlock?
  @objc var onLoadEnd: RCTDirectEventBlock?
  @objc var onLoad: RCTDirectEventBlock?

  
  // MARK: - Properties
  
  @objc var showActivityIndicator = false {
    didSet {
      lazyImageView.placeholderView = UIActivityIndicatorView()
    }
  }
  
  @objc var resizeMode = "cover" {
    didSet {
      lazyImageView.imageView.contentMode = ResizeMode(rawValue: resizeMode)?.contentMode ?? .scaleAspectFill
    }
  }
  
  @objc var progressiveLoadingEnabled = false {
    didSet {
      lazyImageView.isProgressiveImageRenderingEnabled = progressiveLoadingEnabled
    }
  }
  
  @objc var cachePolicy = "discNoCacheControl" {
    didSet {
      lazyImageView.pipeline = CachePolicy(rawValue: cachePolicy)?.pipeline ?? .shared
    }
  }
  
  @objc var rounded: Bool = false {
    didSet {
      guard rounded else {
        return
      }
      lazyImageView.processors = [
        ImageProcessors.Circle()
      ]
    }
  }
  
  @objc var borderRadius: NSNumber? = nil {
    didSet {
    }
  }
  
  // MARK: - Optional Properties
  
  @objc var base64Placeholder: String? {
    didSet {
      guard let base64Placeholder else {
        return
      }
      DispatchQueue.global(qos: .userInteractive).async { [weak self] in
        guard let self else { return }
        guard var image = UIImage(base64Placeholder: base64Placeholder) else {
          return
        }
        if self.rounded {
          let processor = ImageProcessors.Circle()
          if let newImage = processor.process(image) {
            image = newImage
          }
        }
        DispatchQueue.main.async { [weak self] in
          self?.lazyImageView.placeholderImage = image
        }
      }
    }
  }
  
  @objc var blurhash: String? {
    didSet {
      guard let blurhash else {
        return
      }
      DispatchQueue.global(qos: .userInteractive).async { [weak self] in
        guard let self else { return }
        guard var image = UIImage(
          blurHash: blurhash,
          size: .init(width: 32, height: 32)
        ) else {
          return
        }
        if self.rounded {
          let processor = ImageProcessors.Circle()
          if let newImage = processor.process(image) {
            image = newImage
          }
        }
        DispatchQueue.main.async { [weak self] in
          self?.lazyImageView.placeholderImage = image
        }
      }
    }
  }
  
  @objc var failureImage: String? {
    didSet {
      guard let failureImage else {
        return
      }
      DispatchQueue.global(qos: .userInteractive).async { [weak self] in
        guard let self else { return }
        guard
          var image =
            UIImage(blurHash: failureImage, size: .init(width: 32, height: 32))
            ?? UIImage(base64Placeholder: failureImage)
            ?? UIImage(base64Hash: failureImage) else {
          return
        }
        if self.rounded {
          let processor = ImageProcessors.Circle()
          if let newImage = processor.process(image) {
            image = newImage
          }
        }
        DispatchQueue.main.async { [weak self] in
          self?.lazyImageView.failureImage = image
        }
      }
    }
  }
  
  @objc var thumbhash: String? {
    didSet {
      guard let thumbhash else {
        return
      }
      DispatchQueue.global(qos: .userInteractive).async { [weak self] in
        guard let self else { return }
        guard var image = UIImage(base64Hash: thumbhash) else {
          return
        }
        if self.rounded {
          let processor = ImageProcessors.Circle()
          if let newImage = processor.process(image) {
            image = newImage
          }
        }
        DispatchQueue.main.async { [weak self] in
          self?.lazyImageView.placeholderImage = image
        }
      }
    }
  }
  
  @objc var url: String? = nil {
    didSet {
      guard let url else {
        onError?([
          "error": "Expected a valid url but got: \(url ?? "nil")",
        ])
        return
      }
      lazyImageView.url = URL(string: url)
    }
  }
  
  @objc var source: NSDictionary? = nil {
    didSet {
      guard let source else {
        onError?([
          "error": "Expected a valid source but got: nil",
        ])
        return
      }
      
      guard let uri = source["uri"] as? String else {
        onError?([
          "error": "Expected a valid source uri but got: nil",
        ])
        return
      }
      
      let priority = source["priority"] as? String ?? "normal"

      lazyImageView.priority = ImageRequestPriority(rawValue: priority)?.priority
      lazyImageView.url = URL(string: uri)
    }
  }
  
}


fileprivate extension FasterImageView {
  func completionHandler(with result: Result<ImageResponse, Error>) {
    switch(result) {
    case .success(_):
      self.onLoadEnd?([:])
      self.onLoad?([:])
      
    case .failure(let error):
      self.onLoadEnd?([:])
      self.onError?([
        "error": error.localizedDescription,
      ])
    }
  }
  
}
