import CTNotificationService
import CleverTapSDK
import UserNotifications
import OneSignalExtension

class NotificationService: CTNotificationServiceExtension {
  var contentHandler: ((UNNotificationContent) -> Void)?
  var receivedRequest: UNNotificationRequest!
  var bestAttemptContent: UNMutableNotificationContent?

  override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
    self.receivedRequest = request
    self.contentHandler = contentHandler
    self.bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

    if let cleverTapInstance = CleverTap.sharedInstance(), cleverTapInstance.isCleverTapNotification(request.content.userInfo) {
      CleverTap.setDebugLevel(1)
      cleverTapInstance.recordNotificationViewedEvent(withData: request.content.userInfo)
      super.didReceive(request, withContentHandler: contentHandler)
    } else if let bestAttemptContent = bestAttemptContent {
      OneSignalExtension.didReceiveNotificationExtensionRequest(self.receivedRequest, with: bestAttemptContent, withContentHandler: self.contentHandler)
    }
  }

      
  override func serviceExtensionTimeWillExpire() {
      // Called just before the extension will be terminated by the system.
      // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
      if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
        OneSignalExtension.serviceExtensionTimeWillExpireRequest(self.receivedRequest, with: self.bestAttemptContent)
        contentHandler(bestAttemptContent)
      }
  }
}
