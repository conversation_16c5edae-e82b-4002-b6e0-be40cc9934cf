// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		10906889ADA9433580300F00 /* SVN-TimesNewRoman.ttc in Resources */ = {isa = PBXBuildFile; fileRef = 3471CD83F48D4DC98A6AC58B /* SVN-TimesNewRoman.ttc */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		16FFF94411704995BD8CE64D /* LMMonoLt10.ttc in Resources */ = {isa = PBXBuildFile; fileRef = 0C1A5BAB2A194C2BA4FF94C2 /* LMMonoLt10.ttc */; };
		323099CB8B404646A5A401C5 /* SVN-GilroyLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = 9A7CFA98030B4B51BEA7EE6F /* SVN-GilroyLight.otf */; };
		3BE40940ECE345FBB7D99F4C /* SVN-GilroyBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3AE71B01AA29410DAA1D5505 /* SVN-GilroyBold.otf */; };
		45D24384B50D42A991014C47 /* BebasNeueProRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = DF89B8BC9D8B4401BB7F0976 /* BebasNeueProRegular.otf */; };
		4C7FC69E2D956642004F244D /* Fonos Dev.storekit in Resources */ = {isa = PBXBuildFile; fileRef = 4C7FC69D2D956642004F244D /* Fonos Dev.storekit */; };
		4CA181F62D76F5C100E2ED1C /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4CA181F52D76F5C100E2ED1C /* StoreKit.framework */; };
		4CA182022D76F8F400E2ED1C /* NotificationService.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 4CA181FB2D76F8F400E2ED1C /* NotificationService.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		4CA1820B2D76F8FE00E2ED1C /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CA182092D76F8FE00E2ED1C /* NotificationService.swift */; };
		4CD8F5852D770602006FA2E6 /* Config.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */; };
		4CD8F5942D770630006FA2E6 /* libskyepub.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4CD8F58B2D770630006FA2E6 /* libskyepub.a */; };
		4CD8F5A62D770662006FA2E6 /* RNAppShortcut.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5A02D770662006FA2E6 /* RNAppShortcut.m */; };
		4CD8F5A72D770662006FA2E6 /* RNAppShortcutTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5A12D770662006FA2E6 /* RNAppShortcutTypes.swift */; };
		4CD8F5AA2D770662006FA2E6 /* AppShortcuts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5952D770662006FA2E6 /* AppShortcuts.swift */; };
		4CD8F5AF2D770662006FA2E6 /* SharedAppShortcut.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5A22D770662006FA2E6 /* SharedAppShortcut.swift */; };
		4CD8F5B02D770662006FA2E6 /* RNAppShortcut.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F59F2D770662006FA2E6 /* RNAppShortcut.swift */; };
		4CD8F5B42D770668006FA2E6 /* InAppReview.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5B22D770668006FA2E6 /* InAppReview.m */; };
		4CD8F5BE2D770679006FA2E6 /* SharedCarPlay.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5BB2D770679006FA2E6 /* SharedCarPlay.swift */; };
		4CD8F5BF2D770679006FA2E6 /* CarPlayManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5B72D770679006FA2E6 /* CarPlayManager.swift */; };
		4CD8F5C02D770679006FA2E6 /* RNCarPlayTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5BA2D770679006FA2E6 /* RNCarPlayTypes.swift */; };
		4CD8F5C12D770679006FA2E6 /* TemplateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5BC2D770679006FA2E6 /* TemplateManager.swift */; };
		4CD8F5C22D770679006FA2E6 /* PhoneSceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5B52D770679006FA2E6 /* PhoneSceneDelegate.swift */; };
		4CD8F5C32D770679006FA2E6 /* CarPlaySceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5B62D770679006FA2E6 /* CarPlaySceneDelegate.swift */; };
		4CD8F5C42D770679006FA2E6 /* RNCarPlay.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5B82D770679006FA2E6 /* RNCarPlay.swift */; };
		4CD8F5C52D770679006FA2E6 /* RNCarPlay.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5B92D770679006FA2E6 /* RNCarPlay.m */; };
		4CD8F61B2D770681006FA2E6 /* LazyImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F60A2D770681006FA2E6 /* LazyImageView.swift */; };
		4CD8F61C2D770681006FA2E6 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5EC2D770681006FA2E6 /* Extensions.swift */; };
		4CD8F61D2D770681006FA2E6 /* FasterImageViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F60D2D770681006FA2E6 /* FasterImageViewManager.m */; };
		4CD8F61E2D770681006FA2E6 /* Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6092D770681006FA2E6 /* Internal.swift */; };
		4CD8F61F2D770681006FA2E6 /* Log.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E92D770681006FA2E6 /* Log.swift */; };
		4CD8F6202D770681006FA2E6 /* ImageTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5CC2D770681006FA2E6 /* ImageTask.swift */; };
		4CD8F6212D770681006FA2E6 /* RNZendesk.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6172D770681006FA2E6 /* RNZendesk.swift */; };
		4CD8F6222D770681006FA2E6 /* ImageDecoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5FC2D770681006FA2E6 /* ImageDecoding.swift */; };
		4CD8F6232D770681006FA2E6 /* ImageProcessors+Anonymous.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E22D770681006FA2E6 /* ImageProcessors+Anonymous.swift */; };
		4CD8F6242D770681006FA2E6 /* ImageRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6002D770681006FA2E6 /* ImageRequest.swift */; };
		4CD8F6252D770681006FA2E6 /* TaskLoadData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5CE2D770681006FA2E6 /* TaskLoadData.swift */; };
		4CD8F6262D770681006FA2E6 /* DataCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6032D770681006FA2E6 /* DataCache.swift */; };
		4CD8F6272D770681006FA2E6 /* DataCaching.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6062D770681006FA2E6 /* DataCaching.swift */; };
		4CD8F6282D770681006FA2E6 /* ImageDecompression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E42D770681006FA2E6 /* ImageDecompression.swift */; };
		4CD8F6292D770681006FA2E6 /* FasterImageViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F60E2D770681006FA2E6 /* FasterImageViewManager.swift */; };
		4CD8F62A2D770681006FA2E6 /* ImageContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5FF2D770681006FA2E6 /* ImageContainer.swift */; };
		4CD8F62B2D770681006FA2E6 /* ImageProcessing.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5DB2D770681006FA2E6 /* ImageProcessing.swift */; };
		4CD8F62C2D770681006FA2E6 /* ImageCaching.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6052D770681006FA2E6 /* ImageCaching.swift */; };
		4CD8F62D2D770681006FA2E6 /* UIImage+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6102D770681006FA2E6 /* UIImage+Extensions.swift */; };
		4CD8F62E2D770681006FA2E6 /* ImageRequestKeys.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F12D770681006FA2E6 /* ImageRequestKeys.swift */; };
		4CD8F62F2D770681006FA2E6 /* TaskFetchDecodedImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5CD2D770681006FA2E6 /* TaskFetchDecodedImage.swift */; };
		4CD8F6302D770681006FA2E6 /* ImagePipelineConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5C82D770681006FA2E6 /* ImagePipelineConfiguration.swift */; };
		4CD8F6312D770681006FA2E6 /* ImageEncoders+ImageIO.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F72D770681006FA2E6 /* ImageEncoders+ImageIO.swift */; };
		4CD8F6322D770681006FA2E6 /* ImagePublisher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5EF2D770681006FA2E6 /* ImagePublisher.swift */; };
		4CD8F6332D770681006FA2E6 /* ImagePipelineCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5C92D770681006FA2E6 /* ImagePipelineCache.swift */; };
		4CD8F6342D770681006FA2E6 /* ImageDecoderRegistry.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5FD2D770681006FA2E6 /* ImageDecoderRegistry.swift */; };
		4CD8F6352D770681006FA2E6 /* RateLimiter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5EB2D770681006FA2E6 /* RateLimiter.swift */; };
		4CD8F6362D770681006FA2E6 /* RNHeadsetDetect.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6142D770681006FA2E6 /* RNHeadsetDetect.m */; };
		4CD8F6372D770681006FA2E6 /* ImageProcessors+GaussianBlur.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5DD2D770681006FA2E6 /* ImageProcessors+GaussianBlur.swift */; };
		4CD8F6382D770681006FA2E6 /* ImageProcessors+Composition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E32D770681006FA2E6 /* ImageProcessors+Composition.swift */; };
		4CD8F6392D770681006FA2E6 /* CachePolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F60C2D770681006FA2E6 /* CachePolicy.swift */; };
		4CD8F63A2D770681006FA2E6 /* DataPublisher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5EA2D770681006FA2E6 /* DataPublisher.swift */; };
		4CD8F63B2D770681006FA2E6 /* ImageProcessors+Resize.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E12D770681006FA2E6 /* ImageProcessors+Resize.swift */; };
		4CD8F63C2D770681006FA2E6 /* ImageEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F62D770681006FA2E6 /* ImageEncoding.swift */; };
		4CD8F63D2D770681006FA2E6 /* LinkedList.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F22D770681006FA2E6 /* LinkedList.swift */; };
		4CD8F63E2D770681006FA2E6 /* ImageEncoders+Default.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F42D770681006FA2E6 /* ImageEncoders+Default.swift */; };
		4CD8F63F2D770681006FA2E6 /* OperationTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D12D770681006FA2E6 /* OperationTask.swift */; };
		4CD8F6402D770681006FA2E6 /* Atomic.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5ED2D770681006FA2E6 /* Atomic.swift */; };
		4CD8F6412D770681006FA2E6 /* ImageCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6022D770681006FA2E6 /* ImageCache.swift */; };
		4CD8F6422D770681006FA2E6 /* TaskLoadImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D22D770681006FA2E6 /* TaskLoadImage.swift */; };
		4CD8F6432D770681006FA2E6 /* ImageResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6012D770681006FA2E6 /* ImageResponse.swift */; };
		4CD8F6442D770681006FA2E6 /* Graphics.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5EE2D770681006FA2E6 /* Graphics.swift */; };
		4CD8F6452D770681006FA2E6 /* ImagePipelineDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5CA2D770681006FA2E6 /* ImagePipelineDelegate.swift */; };
		4CD8F6462D770681006FA2E6 /* AsyncImageTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D62D770681006FA2E6 /* AsyncImageTask.swift */; };
		4CD8F6472D770681006FA2E6 /* ImagePipelineTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D02D770681006FA2E6 /* ImagePipelineTask.swift */; };
		4CD8F6482D770681006FA2E6 /* ImageProcessors+Circle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E02D770681006FA2E6 /* ImageProcessors+Circle.swift */; };
		4CD8F6492D770681006FA2E6 /* ImagePipeline.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5C62D770681006FA2E6 /* ImagePipeline.swift */; };
		4CD8F64A2D770681006FA2E6 /* ImageProcessors+CoreImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5DE2D770681006FA2E6 /* ImageProcessors+CoreImage.swift */; };
		4CD8F64B2D770681006FA2E6 /* TaskFetchWithPublisher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D42D770681006FA2E6 /* TaskFetchWithPublisher.swift */; };
		4CD8F64C2D770681006FA2E6 /* ImageDecoders+Default.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F92D770681006FA2E6 /* ImageDecoders+Default.swift */; };
		4CD8F64D2D770681006FA2E6 /* ImageProcessingOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5DF2D770681006FA2E6 /* ImageProcessingOptions.swift */; };
		4CD8F64E2D770681006FA2E6 /* ImageProcessors.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5DC2D770681006FA2E6 /* ImageProcessors.swift */; };
		4CD8F64F2D770681006FA2E6 /* ImagePipelineError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5C72D770681006FA2E6 /* ImagePipelineError.swift */; };
		4CD8F6502D770681006FA2E6 /* ImageEncoders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F52D770681006FA2E6 /* ImageEncoders.swift */; };
		4CD8F6512D770681006FA2E6 /* AssetType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5FA2D770681006FA2E6 /* AssetType.swift */; };
		4CD8F6522D770681006FA2E6 /* ImagePrefetcher.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E62D770681006FA2E6 /* ImagePrefetcher.swift */; };
		4CD8F6532D770681006FA2E6 /* DataLoading.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D72D770681006FA2E6 /* DataLoading.swift */; };
		4CD8F6542D770681006FA2E6 /* Priority.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6112D770681006FA2E6 /* Priority.swift */; };
		4CD8F6552D770681006FA2E6 /* Cache.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6042D770681006FA2E6 /* Cache.swift */; };
		4CD8F6562D770681006FA2E6 /* DataLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D82D770681006FA2E6 /* DataLoader.swift */; };
		4CD8F6572D770681006FA2E6 /* Operation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5F02D770681006FA2E6 /* Operation.swift */; };
		4CD8F6582D770681006FA2E6 /* ImageDecoders+Empty.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5FB2D770681006FA2E6 /* ImageDecoders+Empty.swift */; };
		4CD8F6592D770681006FA2E6 /* ResumableData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5E82D770681006FA2E6 /* ResumableData.swift */; };
		4CD8F65A2D770681006FA2E6 /* TaskFetchOriginalImageData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5CF2D770681006FA2E6 /* TaskFetchOriginalImageData.swift */; };
		4CD8F65B2D770681006FA2E6 /* ResizeMode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F60F2D770681006FA2E6 /* ResizeMode.swift */; };
		4CD8F65C2D770681006FA2E6 /* RNZendesk.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6162D770681006FA2E6 /* RNZendesk.m */; };
		4CD8F65D2D770681006FA2E6 /* AsyncTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5D32D770681006FA2E6 /* AsyncTask.swift */; };
		4CD8F65E2D770681006FA2E6 /* ImageProcessors+RoundedCorners.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F5DA2D770681006FA2E6 /* ImageProcessors+RoundedCorners.swift */; };
		4CD8F65F2D770681006FA2E6 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4CD8F6192D770681006FA2E6 /* Localizable.strings */; };
		4CD8F6612D770688006FA2E6 /* AppCenter-Config.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4CD8F6602D770688006FA2E6 /* AppCenter-Config.plist */; };
		4CD8F6652D770693006FA2E6 /* PreventScreenCapture.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6622D770693006FA2E6 /* PreventScreenCapture.swift */; };
		4CD8F6662D770693006FA2E6 /* PreventScreenCapture.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6632D770693006FA2E6 /* PreventScreenCapture.m */; };
		4CD8F6702D770769006FA2E6 /* PlayNewCourseIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6692D770769006FA2E6 /* PlayNewCourseIntent.swift */; };
		4CD8F6712D770769006FA2E6 /* AddBookmarkIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F66E2D770769006FA2E6 /* AddBookmarkIntent.swift */; };
		4CD8F6722D770769006FA2E6 /* AddFavoriteIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F66D2D770769006FA2E6 /* AddFavoriteIntent.swift */; };
		4CD8F6732D770769006FA2E6 /* PlayTopTreningAudioBookIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F66B2D770769006FA2E6 /* PlayTopTreningAudioBookIntent.swift */; };
		4CD8F6742D770769006FA2E6 /* PlayNewAudioBookIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6682D770769006FA2E6 /* PlayNewAudioBookIntent.swift */; };
		4CD8F6752D770769006FA2E6 /* PlayNewEnglishBookIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F66A2D770769006FA2E6 /* PlayNewEnglishBookIntent.swift */; };
		4CD8F6762D770769006FA2E6 /* PlayTopTrendingCourseIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F66C2D770769006FA2E6 /* PlayTopTrendingCourseIntent.swift */; };
		4CD8F6772D770769006FA2E6 /* SetTimerIntent.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F66F2D770769006FA2E6 /* SetTimerIntent.swift */; };
		4CD8F6812D7707C1006FA2E6 /* RNStoryShare.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F67A2D7707C1006FA2E6 /* RNStoryShare.m */; };
		4CD8F6822D7707C1006FA2E6 /* RNFirebase.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6802D7707C1006FA2E6 /* RNFirebase.m */; };
		4CD8F6832D7707C1006FA2E6 /* RNFirebase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F67F2D7707C1006FA2E6 /* RNFirebase.swift */; };
		4CD8F6842D7707C1006FA2E6 /* EpubReader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F67C2D7707C1006FA2E6 /* EpubReader.swift */; };
		4CD8F6852D7707C1006FA2E6 /* RNStoryShare.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F6792D7707C1006FA2E6 /* RNStoryShare.swift */; };
		4CD8F6862D7707C1006FA2E6 /* EpubReader.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F67B2D7707C1006FA2E6 /* EpubReader.m */; };
		4CD8F6872D7707C1006FA2E6 /* RNUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 4CD8F67D2D7707C1006FA2E6 /* RNUtils.m */; };
		4CD8F6882D7707C1006FA2E6 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4CD8F6782D7707C1006FA2E6 /* Images.xcassets */; };
		4CD8F6AD2D7814FF006FA2E6 /* bundles in Resources */ = {isa = PBXBuildFile; fileRef = 4CD8F6AC2D7814FF006FA2E6 /* bundles */; };
		4CD8F6AF2D7AA4BC006FA2E6 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4CD8F6AE2D7AA4BC006FA2E6 /* GoogleService-Info.plist */; };
		5E2BB4D321DA470EB10CD7B1 /* Quicksand.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D44C5EB7F2DA4AEE807001C6 /* Quicksand.ttf */; };
		6D26B968904A4080BFEE2488 /* EBGaramond.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A3053FD03714C919A601E26 /* EBGaramond.ttf */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		81AB9BB82411601600AC10FF /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* BootSplash.storyboard */; };
		8D10EFACEA1D44C4B9CB4AE1 /* icomoon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 58BD3F00E152458F9053B5BE /* icomoon.ttf */; };
		939D57266CBD49F5A8B6D8E9 /* LiterataTT.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CA0390E3241B489CA1234DF4 /* LiterataTT.ttf */; };
		93F1536C5C19477EACF00C9C /* BebasNeueProBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = B2726670E0194524A5A8E79E /* BebasNeueProBold.otf */; };
		9FE1B706E52A8D0743236030 /* Pods_NotificationService.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9C5C3C7F27FF3E39377C58B4 /* Pods_NotificationService.framework */; };
		AF44132CAD55A3B31CB09994 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = B27D9DE8AD4DD4D803164EED /* ExpoModulesProvider.swift */; };
		B3244A6F44A64741962D5576 /* SVN-GilroySemiBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 216EBB16F2AD4F8997F9333E /* SVN-GilroySemiBold.otf */; };
		BEE99586670CFBB0D3573209 /* Pods_FonosMobile.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DB596FB884BBBDE8E4508CFA /* Pods_FonosMobile.framework */; };
		C533E618C31A4E7CA625211B /* SVN-GilroyXBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C9C21C88403F4FDB9A1C513B /* SVN-GilroyXBold.ttf */; };
		E8EC4BF50C8D4EF3AF7FA45B /* SVN-GilroyMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = B0C1DF0208FA45C18F9D9AFC /* SVN-GilroyMedium.otf */; };
		E9F39AD8B687BD69610B0C76 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		FEED52FEE574421DBF17A615 /* SVN-GilroyHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = 5821206616DE47CBBE4362C4 /* SVN-GilroyHeavy.otf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		4CA182002D76F8F400E2ED1C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4CA181FA2D76F8F400E2ED1C;
			remoteInfo = NotificationService;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		4CA182072D76F8F400E2ED1C /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				4CA182022D76F8F400E2ED1C /* NotificationService.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0C1A5BAB2A194C2BA4FF94C2 /* LMMonoLt10.ttc */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = LMMonoLt10.ttc; path = ../src/Assets/fonts/LMMonoLt10.ttc; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* FonosMobile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FonosMobile.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = FonosMobile/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = FonosMobile/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = FonosMobile/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		216EBB16F2AD4F8997F9333E /* SVN-GilroySemiBold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-GilroySemiBold.otf"; path = "../src/Assets/fonts/SVN-GilroySemiBold.otf"; sourceTree = "<group>"; };
		3471CD83F48D4DC98A6AC58B /* SVN-TimesNewRoman.ttc */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-TimesNewRoman.ttc"; path = "../src/Assets/fonts/SVN-TimesNewRoman.ttc"; sourceTree = "<group>"; };
		3AE71B01AA29410DAA1D5505 /* SVN-GilroyBold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-GilroyBold.otf"; path = "../src/Assets/fonts/SVN-GilroyBold.otf"; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-FonosMobile.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FonosMobile.debug.xcconfig"; path = "Target Support Files/Pods-FonosMobile/Pods-FonosMobile.debug.xcconfig"; sourceTree = "<group>"; };
		4C7FC69D2D956642004F244D /* Fonos Dev.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = "Fonos Dev.storekit"; sourceTree = "<group>"; };
		4C7FC69F2D956661004F244D /* Fonos Mobile Staging.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = "Fonos Mobile Staging.storekit"; sourceTree = "<group>"; };
		4CA181F42D76F58A00E2ED1C /* FonosMobile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = FonosMobile.entitlements; path = FonosMobile/FonosMobile.entitlements; sourceTree = "<group>"; };
		4CA181F52D76F5C100E2ED1C /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		4CA181FB2D76F8F400E2ED1C /* NotificationService.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationService.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		4CA182082D76F8FE00E2ED1C /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4CA182092D76F8FE00E2ED1C /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		4CD8F5832D76FBEA006FA2E6 /* NotificationService.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationService.entitlements; sourceTree = "<group>"; };
		4CD8F5842D770602006FA2E6 /* Config.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Config.xcconfig; sourceTree = "<group>"; };
		4CD8F5862D770630006FA2E6 /* Book.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Book.h; sourceTree = "<group>"; };
		4CD8F5872D770630006FA2E6 /* BookInformation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BookInformation.h; sourceTree = "<group>"; };
		4CD8F5882D770630006FA2E6 /* ContentProvider.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ContentProvider.h; sourceTree = "<group>"; };
		4CD8F5892D770630006FA2E6 /* FixedViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FixedViewController.h; sourceTree = "<group>"; };
		4CD8F58A2D770630006FA2E6 /* Highlight.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Highlight.h; sourceTree = "<group>"; };
		4CD8F58B2D770630006FA2E6 /* libskyepub.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libskyepub.a; sourceTree = "<group>"; };
		4CD8F58C2D770630006FA2E6 /* ReflowableViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReflowableViewController.h; sourceTree = "<group>"; };
		4CD8F58D2D770630006FA2E6 /* SearchResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchResult.h; sourceTree = "<group>"; };
		4CD8F58E2D770630006FA2E6 /* SkyDRMControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SkyDRMControl.h; sourceTree = "<group>"; };
		4CD8F58F2D770630006FA2E6 /* SkyEpub.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SkyEpub.h; sourceTree = "<group>"; };
		4CD8F5902D770630006FA2E6 /* SkyKeyManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SkyKeyManager.h; sourceTree = "<group>"; };
		4CD8F5912D770630006FA2E6 /* SkyProvider.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SkyProvider.h; sourceTree = "<group>"; };
		4CD8F5922D770630006FA2E6 /* SpeechSynthesizer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SpeechSynthesizer.h; sourceTree = "<group>"; };
		4CD8F5952D770662006FA2E6 /* AppShortcuts.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppShortcuts.swift; sourceTree = "<group>"; };
		4CD8F59F2D770662006FA2E6 /* RNAppShortcut.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNAppShortcut.swift; sourceTree = "<group>"; };
		4CD8F5A02D770662006FA2E6 /* RNAppShortcut.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNAppShortcut.m; sourceTree = "<group>"; };
		4CD8F5A12D770662006FA2E6 /* RNAppShortcutTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNAppShortcutTypes.swift; sourceTree = "<group>"; };
		4CD8F5A22D770662006FA2E6 /* SharedAppShortcut.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedAppShortcut.swift; sourceTree = "<group>"; };
		4CD8F5B12D770668006FA2E6 /* InAppReview.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InAppReview.h; sourceTree = "<group>"; };
		4CD8F5B22D770668006FA2E6 /* InAppReview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InAppReview.m; sourceTree = "<group>"; };
		4CD8F5B52D770679006FA2E6 /* PhoneSceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhoneSceneDelegate.swift; sourceTree = "<group>"; };
		4CD8F5B62D770679006FA2E6 /* CarPlaySceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CarPlaySceneDelegate.swift; sourceTree = "<group>"; };
		4CD8F5B72D770679006FA2E6 /* CarPlayManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CarPlayManager.swift; sourceTree = "<group>"; };
		4CD8F5B82D770679006FA2E6 /* RNCarPlay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNCarPlay.swift; sourceTree = "<group>"; };
		4CD8F5B92D770679006FA2E6 /* RNCarPlay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNCarPlay.m; sourceTree = "<group>"; };
		4CD8F5BA2D770679006FA2E6 /* RNCarPlayTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNCarPlayTypes.swift; sourceTree = "<group>"; };
		4CD8F5BB2D770679006FA2E6 /* SharedCarPlay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedCarPlay.swift; sourceTree = "<group>"; };
		4CD8F5BC2D770679006FA2E6 /* TemplateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TemplateManager.swift; sourceTree = "<group>"; };
		4CD8F5C62D770681006FA2E6 /* ImagePipeline.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePipeline.swift; sourceTree = "<group>"; };
		4CD8F5C72D770681006FA2E6 /* ImagePipelineError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePipelineError.swift; sourceTree = "<group>"; };
		4CD8F5C82D770681006FA2E6 /* ImagePipelineConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePipelineConfiguration.swift; sourceTree = "<group>"; };
		4CD8F5C92D770681006FA2E6 /* ImagePipelineCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePipelineCache.swift; sourceTree = "<group>"; };
		4CD8F5CA2D770681006FA2E6 /* ImagePipelineDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePipelineDelegate.swift; sourceTree = "<group>"; };
		4CD8F5CC2D770681006FA2E6 /* ImageTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageTask.swift; sourceTree = "<group>"; };
		4CD8F5CD2D770681006FA2E6 /* TaskFetchDecodedImage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskFetchDecodedImage.swift; sourceTree = "<group>"; };
		4CD8F5CE2D770681006FA2E6 /* TaskLoadData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskLoadData.swift; sourceTree = "<group>"; };
		4CD8F5CF2D770681006FA2E6 /* TaskFetchOriginalImageData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskFetchOriginalImageData.swift; sourceTree = "<group>"; };
		4CD8F5D02D770681006FA2E6 /* ImagePipelineTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePipelineTask.swift; sourceTree = "<group>"; };
		4CD8F5D12D770681006FA2E6 /* OperationTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OperationTask.swift; sourceTree = "<group>"; };
		4CD8F5D22D770681006FA2E6 /* TaskLoadImage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskLoadImage.swift; sourceTree = "<group>"; };
		4CD8F5D32D770681006FA2E6 /* AsyncTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncTask.swift; sourceTree = "<group>"; };
		4CD8F5D42D770681006FA2E6 /* TaskFetchWithPublisher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskFetchWithPublisher.swift; sourceTree = "<group>"; };
		4CD8F5D62D770681006FA2E6 /* AsyncImageTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncImageTask.swift; sourceTree = "<group>"; };
		4CD8F5D72D770681006FA2E6 /* DataLoading.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataLoading.swift; sourceTree = "<group>"; };
		4CD8F5D82D770681006FA2E6 /* DataLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataLoader.swift; sourceTree = "<group>"; };
		4CD8F5DA2D770681006FA2E6 /* ImageProcessors+RoundedCorners.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+RoundedCorners.swift"; sourceTree = "<group>"; };
		4CD8F5DB2D770681006FA2E6 /* ImageProcessing.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageProcessing.swift; sourceTree = "<group>"; };
		4CD8F5DC2D770681006FA2E6 /* ImageProcessors.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageProcessors.swift; sourceTree = "<group>"; };
		4CD8F5DD2D770681006FA2E6 /* ImageProcessors+GaussianBlur.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+GaussianBlur.swift"; sourceTree = "<group>"; };
		4CD8F5DE2D770681006FA2E6 /* ImageProcessors+CoreImage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+CoreImage.swift"; sourceTree = "<group>"; };
		4CD8F5DF2D770681006FA2E6 /* ImageProcessingOptions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageProcessingOptions.swift; sourceTree = "<group>"; };
		4CD8F5E02D770681006FA2E6 /* ImageProcessors+Circle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+Circle.swift"; sourceTree = "<group>"; };
		4CD8F5E12D770681006FA2E6 /* ImageProcessors+Resize.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+Resize.swift"; sourceTree = "<group>"; };
		4CD8F5E22D770681006FA2E6 /* ImageProcessors+Anonymous.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+Anonymous.swift"; sourceTree = "<group>"; };
		4CD8F5E32D770681006FA2E6 /* ImageProcessors+Composition.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageProcessors+Composition.swift"; sourceTree = "<group>"; };
		4CD8F5E42D770681006FA2E6 /* ImageDecompression.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDecompression.swift; sourceTree = "<group>"; };
		4CD8F5E62D770681006FA2E6 /* ImagePrefetcher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePrefetcher.swift; sourceTree = "<group>"; };
		4CD8F5E82D770681006FA2E6 /* ResumableData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResumableData.swift; sourceTree = "<group>"; };
		4CD8F5E92D770681006FA2E6 /* Log.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Log.swift; sourceTree = "<group>"; };
		4CD8F5EA2D770681006FA2E6 /* DataPublisher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataPublisher.swift; sourceTree = "<group>"; };
		4CD8F5EB2D770681006FA2E6 /* RateLimiter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RateLimiter.swift; sourceTree = "<group>"; };
		4CD8F5EC2D770681006FA2E6 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		4CD8F5ED2D770681006FA2E6 /* Atomic.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Atomic.swift; sourceTree = "<group>"; };
		4CD8F5EE2D770681006FA2E6 /* Graphics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Graphics.swift; sourceTree = "<group>"; };
		4CD8F5EF2D770681006FA2E6 /* ImagePublisher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePublisher.swift; sourceTree = "<group>"; };
		4CD8F5F02D770681006FA2E6 /* Operation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Operation.swift; sourceTree = "<group>"; };
		4CD8F5F12D770681006FA2E6 /* ImageRequestKeys.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageRequestKeys.swift; sourceTree = "<group>"; };
		4CD8F5F22D770681006FA2E6 /* LinkedList.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LinkedList.swift; sourceTree = "<group>"; };
		4CD8F5F42D770681006FA2E6 /* ImageEncoders+Default.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageEncoders+Default.swift"; sourceTree = "<group>"; };
		4CD8F5F52D770681006FA2E6 /* ImageEncoders.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageEncoders.swift; sourceTree = "<group>"; };
		4CD8F5F62D770681006FA2E6 /* ImageEncoding.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageEncoding.swift; sourceTree = "<group>"; };
		4CD8F5F72D770681006FA2E6 /* ImageEncoders+ImageIO.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageEncoders+ImageIO.swift"; sourceTree = "<group>"; };
		4CD8F5F92D770681006FA2E6 /* ImageDecoders+Default.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageDecoders+Default.swift"; sourceTree = "<group>"; };
		4CD8F5FA2D770681006FA2E6 /* AssetType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AssetType.swift; sourceTree = "<group>"; };
		4CD8F5FB2D770681006FA2E6 /* ImageDecoders+Empty.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ImageDecoders+Empty.swift"; sourceTree = "<group>"; };
		4CD8F5FC2D770681006FA2E6 /* ImageDecoding.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDecoding.swift; sourceTree = "<group>"; };
		4CD8F5FD2D770681006FA2E6 /* ImageDecoderRegistry.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDecoderRegistry.swift; sourceTree = "<group>"; };
		4CD8F5FF2D770681006FA2E6 /* ImageContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageContainer.swift; sourceTree = "<group>"; };
		4CD8F6002D770681006FA2E6 /* ImageRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageRequest.swift; sourceTree = "<group>"; };
		4CD8F6012D770681006FA2E6 /* ImageResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageResponse.swift; sourceTree = "<group>"; };
		4CD8F6022D770681006FA2E6 /* ImageCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageCache.swift; sourceTree = "<group>"; };
		4CD8F6032D770681006FA2E6 /* DataCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataCache.swift; sourceTree = "<group>"; };
		4CD8F6042D770681006FA2E6 /* Cache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Cache.swift; sourceTree = "<group>"; };
		4CD8F6052D770681006FA2E6 /* ImageCaching.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageCaching.swift; sourceTree = "<group>"; };
		4CD8F6062D770681006FA2E6 /* DataCaching.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataCaching.swift; sourceTree = "<group>"; };
		4CD8F6092D770681006FA2E6 /* Internal.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Internal.swift; sourceTree = "<group>"; };
		4CD8F60A2D770681006FA2E6 /* LazyImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LazyImageView.swift; sourceTree = "<group>"; };
		4CD8F60C2D770681006FA2E6 /* CachePolicy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CachePolicy.swift; sourceTree = "<group>"; };
		4CD8F60D2D770681006FA2E6 /* FasterImageViewManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FasterImageViewManager.m; sourceTree = "<group>"; };
		4CD8F60E2D770681006FA2E6 /* FasterImageViewManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FasterImageViewManager.swift; sourceTree = "<group>"; };
		4CD8F60F2D770681006FA2E6 /* ResizeMode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResizeMode.swift; sourceTree = "<group>"; };
		4CD8F6102D770681006FA2E6 /* UIImage+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIImage+Extensions.swift"; sourceTree = "<group>"; };
		4CD8F6112D770681006FA2E6 /* Priority.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Priority.swift; sourceTree = "<group>"; };
		4CD8F6132D770681006FA2E6 /* RNHeadsetDetect.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNHeadsetDetect.h; sourceTree = "<group>"; };
		4CD8F6142D770681006FA2E6 /* RNHeadsetDetect.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNHeadsetDetect.m; sourceTree = "<group>"; };
		4CD8F6162D770681006FA2E6 /* RNZendesk.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNZendesk.m; sourceTree = "<group>"; };
		4CD8F6172D770681006FA2E6 /* RNZendesk.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNZendesk.swift; sourceTree = "<group>"; };
		4CD8F6182D770681006FA2E6 /* RNZendesk-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNZendesk-Bridging-Header.h"; sourceTree = "<group>"; };
		4CD8F6192D770681006FA2E6 /* Localizable.strings */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; path = Localizable.strings; sourceTree = "<group>"; };
		4CD8F6602D770688006FA2E6 /* AppCenter-Config.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "AppCenter-Config.plist"; sourceTree = "<group>"; };
		4CD8F6622D770693006FA2E6 /* PreventScreenCapture.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreventScreenCapture.swift; sourceTree = "<group>"; };
		4CD8F6632D770693006FA2E6 /* PreventScreenCapture.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PreventScreenCapture.m; sourceTree = "<group>"; };
		4CD8F6682D770769006FA2E6 /* PlayNewAudioBookIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayNewAudioBookIntent.swift; sourceTree = "<group>"; };
		4CD8F6692D770769006FA2E6 /* PlayNewCourseIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayNewCourseIntent.swift; sourceTree = "<group>"; };
		4CD8F66A2D770769006FA2E6 /* PlayNewEnglishBookIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayNewEnglishBookIntent.swift; sourceTree = "<group>"; };
		4CD8F66B2D770769006FA2E6 /* PlayTopTreningAudioBookIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayTopTreningAudioBookIntent.swift; sourceTree = "<group>"; };
		4CD8F66C2D770769006FA2E6 /* PlayTopTrendingCourseIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayTopTrendingCourseIntent.swift; sourceTree = "<group>"; };
		4CD8F66D2D770769006FA2E6 /* AddFavoriteIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddFavoriteIntent.swift; sourceTree = "<group>"; };
		4CD8F66E2D770769006FA2E6 /* AddBookmarkIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddBookmarkIntent.swift; sourceTree = "<group>"; };
		4CD8F66F2D770769006FA2E6 /* SetTimerIntent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetTimerIntent.swift; sourceTree = "<group>"; };
		4CD8F6782D7707C1006FA2E6 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		4CD8F6792D7707C1006FA2E6 /* RNStoryShare.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNStoryShare.swift; sourceTree = "<group>"; };
		4CD8F67A2D7707C1006FA2E6 /* RNStoryShare.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNStoryShare.m; sourceTree = "<group>"; };
		4CD8F67B2D7707C1006FA2E6 /* EpubReader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EpubReader.m; sourceTree = "<group>"; };
		4CD8F67C2D7707C1006FA2E6 /* EpubReader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EpubReader.swift; sourceTree = "<group>"; };
		4CD8F67D2D7707C1006FA2E6 /* RNUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNUtils.m; sourceTree = "<group>"; };
		4CD8F67E2D7707C1006FA2E6 /* RNUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNUtils.h; sourceTree = "<group>"; };
		4CD8F67F2D7707C1006FA2E6 /* RNFirebase.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RNFirebase.swift; sourceTree = "<group>"; };
		4CD8F6802D7707C1006FA2E6 /* RNFirebase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNFirebase.m; sourceTree = "<group>"; };
		4CD8F6952D774A6A006FA2E6 /* Bridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Bridge.h; sourceTree = "<group>"; };
		4CD8F6AC2D7814FF006FA2E6 /* bundles */ = {isa = PBXFileReference; lastKnownFileType = folder; name = bundles; path = ../node/bundles; sourceTree = "<group>"; };
		4CD8F6AE2D7AA4BC006FA2E6 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "FonosMobile/GoogleService-Info.plist"; sourceTree = "<group>"; };
		4CD8F6B42D7AA813006FA2E6 /* Pods-FonosMobile.debugstaging.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-FonosMobile/Pods-FonosMobile.debugstaging.xcconfig"; sourceTree = "<group>"; };
		4CD8F6B52D7AA850006FA2E6 /* Pods-FonosMobile.releasestaging.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-FonosMobile/Pods-FonosMobile.releasestaging.xcconfig"; sourceTree = "<group>"; };
		4CD8F6B92D7AB2AD006FA2E6 /* Pods-FonosMobile.debugproduction.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-FonosMobile/Pods-FonosMobile.debugproduction.xcconfig"; sourceTree = "<group>"; };
		4CD8F6BB2D7AB2E1006FA2E6 /* Pods-FonosMobile.releaseproduction.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-FonosMobile/Pods-FonosMobile.releaseproduction.xcconfig"; sourceTree = "<group>"; };
		4CD8F6C22D7AB4EE006FA2E6 /* Pods-NotificationService.debugstaging.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.debugstaging.xcconfig"; sourceTree = "<group>"; };
		4CD8F6C32D7AB4F9006FA2E6 /* Pods-NotificationService.releasestaging.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.releasestaging.xcconfig"; sourceTree = "<group>"; };
		4CD8F6C42D7AB506006FA2E6 /* Pods-NotificationService.debugproduction.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.debugproduction.xcconfig"; sourceTree = "<group>"; };
		4CD8F6C52D7AB519006FA2E6 /* Pods-NotificationService.releaseproduction.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.releaseproduction.xcconfig"; sourceTree = "<group>"; };
		5709B34CF0A7D63546082F79 /* Pods-FonosMobile.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FonosMobile.release.xcconfig"; path = "Target Support Files/Pods-FonosMobile/Pods-FonosMobile.release.xcconfig"; sourceTree = "<group>"; };
		5821206616DE47CBBE4362C4 /* SVN-GilroyHeavy.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-GilroyHeavy.otf"; path = "../src/Assets/fonts/SVN-GilroyHeavy.otf"; sourceTree = "<group>"; };
		58BD3F00E152458F9053B5BE /* icomoon.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = icomoon.ttf; path = ../src/Assets/fonts/icomoon.ttf; sourceTree = "<group>"; };
		59D6BADC6AD665E62851D83B /* Pods-NotificationService.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationService.release.xcconfig"; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.release.xcconfig"; sourceTree = "<group>"; };
		6A3053FD03714C919A601E26 /* EBGaramond.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EBGaramond.ttf; path = ../src/Assets/fonts/EBGaramond.ttf; sourceTree = "<group>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = FonosMobile/AppDelegate.swift; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* BootSplash.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = BootSplash.storyboard; path = FonosMobile/BootSplash.storyboard; sourceTree = "<group>"; };
		9A7CFA98030B4B51BEA7EE6F /* SVN-GilroyLight.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-GilroyLight.otf"; path = "../src/Assets/fonts/SVN-GilroyLight.otf"; sourceTree = "<group>"; };
		9C5C3C7F27FF3E39377C58B4 /* Pods_NotificationService.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NotificationService.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A690D52C2EFDC1C0B1C03D35 /* Pods-NotificationService.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationService.debug.xcconfig"; path = "Target Support Files/Pods-NotificationService/Pods-NotificationService.debug.xcconfig"; sourceTree = "<group>"; };
		B0C1DF0208FA45C18F9D9AFC /* SVN-GilroyMedium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-GilroyMedium.otf"; path = "../src/Assets/fonts/SVN-GilroyMedium.otf"; sourceTree = "<group>"; };
		B2726670E0194524A5A8E79E /* BebasNeueProBold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = BebasNeueProBold.otf; path = ../src/Assets/fonts/BebasNeueProBold.otf; sourceTree = "<group>"; };
		B27D9DE8AD4DD4D803164EED /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-FonosMobile/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		C9C21C88403F4FDB9A1C513B /* SVN-GilroyXBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SVN-GilroyXBold.ttf"; path = "../src/Assets/fonts/SVN-GilroyXBold.ttf"; sourceTree = "<group>"; };
		CA0390E3241B489CA1234DF4 /* LiterataTT.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = LiterataTT.ttf; path = ../src/Assets/fonts/LiterataTT.ttf; sourceTree = "<group>"; };
		D44C5EB7F2DA4AEE807001C6 /* Quicksand.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Quicksand.ttf; path = ../src/Assets/fonts/Quicksand.ttf; sourceTree = "<group>"; };
		DB596FB884BBBDE8E4508CFA /* Pods_FonosMobile.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_FonosMobile.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF89B8BC9D8B4401BB7F0976 /* BebasNeueProRegular.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = BebasNeueProRegular.otf; path = ../src/Assets/fonts/BebasNeueProRegular.otf; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4CD8F5942D770630006FA2E6 /* libskyepub.a in Frameworks */,
				BEE99586670CFBB0D3573209 /* Pods_FonosMobile.framework in Frameworks */,
				4CA181F62D76F5C100E2ED1C /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CA181F82D76F8F400E2ED1C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				9FE1B706E52A8D0743236030 /* Pods_NotificationService.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* FonosMobile */ = {
			isa = PBXGroup;
			children = (
				4CD8F6782D7707C1006FA2E6 /* Images.xcassets */,
				4CD8F6792D7707C1006FA2E6 /* RNStoryShare.swift */,
				4CD8F67A2D7707C1006FA2E6 /* RNStoryShare.m */,
				4CD8F67B2D7707C1006FA2E6 /* EpubReader.m */,
				4CD8F67C2D7707C1006FA2E6 /* EpubReader.swift */,
				4CD8F67D2D7707C1006FA2E6 /* RNUtils.m */,
				4CD8F67E2D7707C1006FA2E6 /* RNUtils.h */,
				4CD8F67F2D7707C1006FA2E6 /* RNFirebase.swift */,
				4CD8F6802D7707C1006FA2E6 /* RNFirebase.m */,
				4CA181F42D76F58A00E2ED1C /* FonosMobile.entitlements */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* BootSplash.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				4CD8F6AE2D7AA4BC006FA2E6 /* GoogleService-Info.plist */,
			);
			name = FonosMobile;
			sourceTree = "<group>";
		};
		1DB561BE2BAF665C808CFA68 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				CBA5EBBEBF69BA9459CF7489 /* FonosMobile */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				4CA181F52D76F5C100E2ED1C /* StoreKit.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				DB596FB884BBBDE8E4508CFA /* Pods_FonosMobile.framework */,
				9C5C3C7F27FF3E39377C58B4 /* Pods_NotificationService.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		4CA1820A2D76F8FE00E2ED1C /* NotificationService */ = {
			isa = PBXGroup;
			children = (
				4CD8F5832D76FBEA006FA2E6 /* NotificationService.entitlements */,
				4CA182082D76F8FE00E2ED1C /* Info.plist */,
				4CA182092D76F8FE00E2ED1C /* NotificationService.swift */,
			);
			path = NotificationService;
			sourceTree = "<group>";
		};
		4CD8F5932D770630006FA2E6 /* SkyEpub */ = {
			isa = PBXGroup;
			children = (
				4CD8F5862D770630006FA2E6 /* Book.h */,
				4CD8F5872D770630006FA2E6 /* BookInformation.h */,
				4CD8F5882D770630006FA2E6 /* ContentProvider.h */,
				4CD8F5892D770630006FA2E6 /* FixedViewController.h */,
				4CD8F58A2D770630006FA2E6 /* Highlight.h */,
				4CD8F58B2D770630006FA2E6 /* libskyepub.a */,
				4CD8F58C2D770630006FA2E6 /* ReflowableViewController.h */,
				4CD8F58D2D770630006FA2E6 /* SearchResult.h */,
				4CD8F58E2D770630006FA2E6 /* SkyDRMControl.h */,
				4CD8F58F2D770630006FA2E6 /* SkyEpub.h */,
				4CD8F5902D770630006FA2E6 /* SkyKeyManager.h */,
				4CD8F5912D770630006FA2E6 /* SkyProvider.h */,
				4CD8F5922D770630006FA2E6 /* SpeechSynthesizer.h */,
			);
			path = SkyEpub;
			sourceTree = "<group>";
		};
		4CD8F5A32D770662006FA2E6 /* AppShortcuts */ = {
			isa = PBXGroup;
			children = (
				4CD8F6672D77074D006FA2E6 /* AppIntents */,
				4CD8F5952D770662006FA2E6 /* AppShortcuts.swift */,
				4CD8F59F2D770662006FA2E6 /* RNAppShortcut.swift */,
				4CD8F5A02D770662006FA2E6 /* RNAppShortcut.m */,
				4CD8F5A12D770662006FA2E6 /* RNAppShortcutTypes.swift */,
				4CD8F5A22D770662006FA2E6 /* SharedAppShortcut.swift */,
			);
			path = AppShortcuts;
			sourceTree = "<group>";
		};
		4CD8F5B32D770668006FA2E6 /* InAppReview */ = {
			isa = PBXGroup;
			children = (
				4CD8F5B12D770668006FA2E6 /* InAppReview.h */,
				4CD8F5B22D770668006FA2E6 /* InAppReview.m */,
			);
			path = InAppReview;
			sourceTree = "<group>";
		};
		4CD8F5BD2D770679006FA2E6 /* CarPlay */ = {
			isa = PBXGroup;
			children = (
				4CD8F5B52D770679006FA2E6 /* PhoneSceneDelegate.swift */,
				4CD8F5B62D770679006FA2E6 /* CarPlaySceneDelegate.swift */,
				4CD8F5B72D770679006FA2E6 /* CarPlayManager.swift */,
				4CD8F5B82D770679006FA2E6 /* RNCarPlay.swift */,
				4CD8F5B92D770679006FA2E6 /* RNCarPlay.m */,
				4CD8F5BA2D770679006FA2E6 /* RNCarPlayTypes.swift */,
				4CD8F5BB2D770679006FA2E6 /* SharedCarPlay.swift */,
				4CD8F5BC2D770679006FA2E6 /* TemplateManager.swift */,
			);
			path = CarPlay;
			sourceTree = "<group>";
		};
		4CD8F5CB2D770681006FA2E6 /* Pipeline */ = {
			isa = PBXGroup;
			children = (
				4CD8F5C62D770681006FA2E6 /* ImagePipeline.swift */,
				4CD8F5C72D770681006FA2E6 /* ImagePipelineError.swift */,
				4CD8F5C82D770681006FA2E6 /* ImagePipelineConfiguration.swift */,
				4CD8F5C92D770681006FA2E6 /* ImagePipelineCache.swift */,
				4CD8F5CA2D770681006FA2E6 /* ImagePipelineDelegate.swift */,
			);
			path = Pipeline;
			sourceTree = "<group>";
		};
		4CD8F5D52D770681006FA2E6 /* Tasks */ = {
			isa = PBXGroup;
			children = (
				4CD8F5CD2D770681006FA2E6 /* TaskFetchDecodedImage.swift */,
				4CD8F5CE2D770681006FA2E6 /* TaskLoadData.swift */,
				4CD8F5CF2D770681006FA2E6 /* TaskFetchOriginalImageData.swift */,
				4CD8F5D02D770681006FA2E6 /* ImagePipelineTask.swift */,
				4CD8F5D12D770681006FA2E6 /* OperationTask.swift */,
				4CD8F5D22D770681006FA2E6 /* TaskLoadImage.swift */,
				4CD8F5D32D770681006FA2E6 /* AsyncTask.swift */,
				4CD8F5D42D770681006FA2E6 /* TaskFetchWithPublisher.swift */,
			);
			path = Tasks;
			sourceTree = "<group>";
		};
		4CD8F5D92D770681006FA2E6 /* Loading */ = {
			isa = PBXGroup;
			children = (
				4CD8F5D72D770681006FA2E6 /* DataLoading.swift */,
				4CD8F5D82D770681006FA2E6 /* DataLoader.swift */,
			);
			path = Loading;
			sourceTree = "<group>";
		};
		4CD8F5E52D770681006FA2E6 /* Processing */ = {
			isa = PBXGroup;
			children = (
				4CD8F5DA2D770681006FA2E6 /* ImageProcessors+RoundedCorners.swift */,
				4CD8F5DB2D770681006FA2E6 /* ImageProcessing.swift */,
				4CD8F5DC2D770681006FA2E6 /* ImageProcessors.swift */,
				4CD8F5DD2D770681006FA2E6 /* ImageProcessors+GaussianBlur.swift */,
				4CD8F5DE2D770681006FA2E6 /* ImageProcessors+CoreImage.swift */,
				4CD8F5DF2D770681006FA2E6 /* ImageProcessingOptions.swift */,
				4CD8F5E02D770681006FA2E6 /* ImageProcessors+Circle.swift */,
				4CD8F5E12D770681006FA2E6 /* ImageProcessors+Resize.swift */,
				4CD8F5E22D770681006FA2E6 /* ImageProcessors+Anonymous.swift */,
				4CD8F5E32D770681006FA2E6 /* ImageProcessors+Composition.swift */,
				4CD8F5E42D770681006FA2E6 /* ImageDecompression.swift */,
			);
			path = Processing;
			sourceTree = "<group>";
		};
		4CD8F5E72D770681006FA2E6 /* Prefetching */ = {
			isa = PBXGroup;
			children = (
				4CD8F5E62D770681006FA2E6 /* ImagePrefetcher.swift */,
			);
			path = Prefetching;
			sourceTree = "<group>";
		};
		4CD8F5F32D770681006FA2E6 /* Internal */ = {
			isa = PBXGroup;
			children = (
				4CD8F5E82D770681006FA2E6 /* ResumableData.swift */,
				4CD8F5E92D770681006FA2E6 /* Log.swift */,
				4CD8F5EA2D770681006FA2E6 /* DataPublisher.swift */,
				4CD8F5EB2D770681006FA2E6 /* RateLimiter.swift */,
				4CD8F5EC2D770681006FA2E6 /* Extensions.swift */,
				4CD8F5ED2D770681006FA2E6 /* Atomic.swift */,
				4CD8F5EE2D770681006FA2E6 /* Graphics.swift */,
				4CD8F5EF2D770681006FA2E6 /* ImagePublisher.swift */,
				4CD8F5F02D770681006FA2E6 /* Operation.swift */,
				4CD8F5F12D770681006FA2E6 /* ImageRequestKeys.swift */,
				4CD8F5F22D770681006FA2E6 /* LinkedList.swift */,
			);
			path = Internal;
			sourceTree = "<group>";
		};
		4CD8F5F82D770681006FA2E6 /* Encoding */ = {
			isa = PBXGroup;
			children = (
				4CD8F5F42D770681006FA2E6 /* ImageEncoders+Default.swift */,
				4CD8F5F52D770681006FA2E6 /* ImageEncoders.swift */,
				4CD8F5F62D770681006FA2E6 /* ImageEncoding.swift */,
				4CD8F5F72D770681006FA2E6 /* ImageEncoders+ImageIO.swift */,
			);
			path = Encoding;
			sourceTree = "<group>";
		};
		4CD8F5FE2D770681006FA2E6 /* Decoding */ = {
			isa = PBXGroup;
			children = (
				4CD8F5F92D770681006FA2E6 /* ImageDecoders+Default.swift */,
				4CD8F5FA2D770681006FA2E6 /* AssetType.swift */,
				4CD8F5FB2D770681006FA2E6 /* ImageDecoders+Empty.swift */,
				4CD8F5FC2D770681006FA2E6 /* ImageDecoding.swift */,
				4CD8F5FD2D770681006FA2E6 /* ImageDecoderRegistry.swift */,
			);
			path = Decoding;
			sourceTree = "<group>";
		};
		4CD8F6072D770681006FA2E6 /* Caching */ = {
			isa = PBXGroup;
			children = (
				4CD8F6022D770681006FA2E6 /* ImageCache.swift */,
				4CD8F6032D770681006FA2E6 /* DataCache.swift */,
				4CD8F6042D770681006FA2E6 /* Cache.swift */,
				4CD8F6052D770681006FA2E6 /* ImageCaching.swift */,
				4CD8F6062D770681006FA2E6 /* DataCaching.swift */,
			);
			path = Caching;
			sourceTree = "<group>";
		};
		4CD8F6082D770681006FA2E6 /* Nuke */ = {
			isa = PBXGroup;
			children = (
				4CD8F5CB2D770681006FA2E6 /* Pipeline */,
				4CD8F5CC2D770681006FA2E6 /* ImageTask.swift */,
				4CD8F5D52D770681006FA2E6 /* Tasks */,
				4CD8F5D62D770681006FA2E6 /* AsyncImageTask.swift */,
				4CD8F5D92D770681006FA2E6 /* Loading */,
				4CD8F5E52D770681006FA2E6 /* Processing */,
				4CD8F5E72D770681006FA2E6 /* Prefetching */,
				4CD8F5F32D770681006FA2E6 /* Internal */,
				4CD8F5F82D770681006FA2E6 /* Encoding */,
				4CD8F5FE2D770681006FA2E6 /* Decoding */,
				4CD8F5FF2D770681006FA2E6 /* ImageContainer.swift */,
				4CD8F6002D770681006FA2E6 /* ImageRequest.swift */,
				4CD8F6012D770681006FA2E6 /* ImageResponse.swift */,
				4CD8F6072D770681006FA2E6 /* Caching */,
			);
			path = Nuke;
			sourceTree = "<group>";
		};
		4CD8F60B2D770681006FA2E6 /* NukeUI */ = {
			isa = PBXGroup;
			children = (
				4CD8F6092D770681006FA2E6 /* Internal.swift */,
				4CD8F60A2D770681006FA2E6 /* LazyImageView.swift */,
			);
			path = NukeUI;
			sourceTree = "<group>";
		};
		4CD8F6122D770681006FA2E6 /* FasterImage */ = {
			isa = PBXGroup;
			children = (
				4CD8F6082D770681006FA2E6 /* Nuke */,
				4CD8F60B2D770681006FA2E6 /* NukeUI */,
				4CD8F60C2D770681006FA2E6 /* CachePolicy.swift */,
				4CD8F60D2D770681006FA2E6 /* FasterImageViewManager.m */,
				4CD8F60E2D770681006FA2E6 /* FasterImageViewManager.swift */,
				4CD8F60F2D770681006FA2E6 /* ResizeMode.swift */,
				4CD8F6102D770681006FA2E6 /* UIImage+Extensions.swift */,
				4CD8F6112D770681006FA2E6 /* Priority.swift */,
			);
			path = FasterImage;
			sourceTree = "<group>";
		};
		4CD8F6152D770681006FA2E6 /* RNHeadsetDetect */ = {
			isa = PBXGroup;
			children = (
				4CD8F6132D770681006FA2E6 /* RNHeadsetDetect.h */,
				4CD8F6142D770681006FA2E6 /* RNHeadsetDetect.m */,
			);
			path = RNHeadsetDetect;
			sourceTree = "<group>";
		};
		4CD8F61A2D770681006FA2E6 /* RNZendesk */ = {
			isa = PBXGroup;
			children = (
				4CD8F6162D770681006FA2E6 /* RNZendesk.m */,
				4CD8F6172D770681006FA2E6 /* RNZendesk.swift */,
				4CD8F6182D770681006FA2E6 /* RNZendesk-Bridging-Header.h */,
				4CD8F6192D770681006FA2E6 /* Localizable.strings */,
			);
			path = RNZendesk;
			sourceTree = "<group>";
		};
		4CD8F6642D770693006FA2E6 /* PreventScreenCapture */ = {
			isa = PBXGroup;
			children = (
				4CD8F6622D770693006FA2E6 /* PreventScreenCapture.swift */,
				4CD8F6632D770693006FA2E6 /* PreventScreenCapture.m */,
			);
			path = PreventScreenCapture;
			sourceTree = "<group>";
		};
		4CD8F6672D77074D006FA2E6 /* AppIntents */ = {
			isa = PBXGroup;
			children = (
				4CD8F6682D770769006FA2E6 /* PlayNewAudioBookIntent.swift */,
				4CD8F6692D770769006FA2E6 /* PlayNewCourseIntent.swift */,
				4CD8F66A2D770769006FA2E6 /* PlayNewEnglishBookIntent.swift */,
				4CD8F66B2D770769006FA2E6 /* PlayTopTreningAudioBookIntent.swift */,
				4CD8F66C2D770769006FA2E6 /* PlayTopTrendingCourseIntent.swift */,
				4CD8F66D2D770769006FA2E6 /* AddFavoriteIntent.swift */,
				4CD8F66E2D770769006FA2E6 /* AddBookmarkIntent.swift */,
				4CD8F66F2D770769006FA2E6 /* SetTimerIntent.swift */,
			);
			path = AppIntents;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				4C7FC69F2D956661004F244D /* Fonos Mobile Staging.storekit */,
				4C7FC69D2D956642004F244D /* Fonos Dev.storekit */,
				4CD8F6AC2D7814FF006FA2E6 /* bundles */,
				4CD8F6952D774A6A006FA2E6 /* Bridge.h */,
				13B07FAE1A68108700A75B9A /* FonosMobile */,
				4CD8F6642D770693006FA2E6 /* PreventScreenCapture */,
				4CD8F6602D770688006FA2E6 /* AppCenter-Config.plist */,
				4CD8F6122D770681006FA2E6 /* FasterImage */,
				4CD8F6152D770681006FA2E6 /* RNHeadsetDetect */,
				4CD8F61A2D770681006FA2E6 /* RNZendesk */,
				4CD8F5BD2D770679006FA2E6 /* CarPlay */,
				4CD8F5B32D770668006FA2E6 /* InAppReview */,
				4CD8F5A32D770662006FA2E6 /* AppShortcuts */,
				4CD8F5932D770630006FA2E6 /* SkyEpub */,
				4CD8F5842D770602006FA2E6 /* Config.xcconfig */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				4CA1820A2D76F8FE00E2ED1C /* NotificationService */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				1DB561BE2BAF665C808CFA68 /* ExpoModulesProviders */,
				FB18F550FACD4C81994573FD /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* FonosMobile.app */,
				4CA181FB2D76F8F400E2ED1C /* NotificationService.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				4CD8F6C22D7AB4EE006FA2E6 /* Pods-NotificationService.debugstaging.xcconfig */,
				4CD8F6BB2D7AB2E1006FA2E6 /* Pods-FonosMobile.releaseproduction.xcconfig */,
				4CD8F6B92D7AB2AD006FA2E6 /* Pods-FonosMobile.debugproduction.xcconfig */,
				4CD8F6B52D7AA850006FA2E6 /* Pods-FonosMobile.releasestaging.xcconfig */,
				4CD8F6B42D7AA813006FA2E6 /* Pods-FonosMobile.debugstaging.xcconfig */,
				3B4392A12AC88292D35C810B /* Pods-FonosMobile.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-FonosMobile.release.xcconfig */,
				A690D52C2EFDC1C0B1C03D35 /* Pods-NotificationService.debug.xcconfig */,
				4CD8F6C42D7AB506006FA2E6 /* Pods-NotificationService.debugproduction.xcconfig */,
				59D6BADC6AD665E62851D83B /* Pods-NotificationService.release.xcconfig */,
				4CD8F6C52D7AB519006FA2E6 /* Pods-NotificationService.releaseproduction.xcconfig */,
				4CD8F6C32D7AB4F9006FA2E6 /* Pods-NotificationService.releasestaging.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		CBA5EBBEBF69BA9459CF7489 /* FonosMobile */ = {
			isa = PBXGroup;
			children = (
				B27D9DE8AD4DD4D803164EED /* ExpoModulesProvider.swift */,
			);
			name = FonosMobile;
			sourceTree = "<group>";
		};
		FB18F550FACD4C81994573FD /* Resources */ = {
			isa = PBXGroup;
			children = (
				B2726670E0194524A5A8E79E /* BebasNeueProBold.otf */,
				DF89B8BC9D8B4401BB7F0976 /* BebasNeueProRegular.otf */,
				3AE71B01AA29410DAA1D5505 /* SVN-GilroyBold.otf */,
				5821206616DE47CBBE4362C4 /* SVN-GilroyHeavy.otf */,
				9A7CFA98030B4B51BEA7EE6F /* SVN-GilroyLight.otf */,
				B0C1DF0208FA45C18F9D9AFC /* SVN-GilroyMedium.otf */,
				216EBB16F2AD4F8997F9333E /* SVN-GilroySemiBold.otf */,
				6A3053FD03714C919A601E26 /* EBGaramond.ttf */,
				CA0390E3241B489CA1234DF4 /* LiterataTT.ttf */,
				D44C5EB7F2DA4AEE807001C6 /* Quicksand.ttf */,
				C9C21C88403F4FDB9A1C513B /* SVN-GilroyXBold.ttf */,
				0C1A5BAB2A194C2BA4FF94C2 /* LMMonoLt10.ttc */,
				3471CD83F48D4DC98A6AC58B /* SVN-TimesNewRoman.ttc */,
				58BD3F00E152458F9053B5BE /* icomoon.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* FonosMobile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "FonosMobile" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				4CA182072D76F8F400E2ED1C /* Embed Foundation Extensions */,
				A880DFE9F54D7BDCC1E55B60 /* [Expo] Configure project */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				C099B964394D7916D3328617 /* [CP-User] [RNFB] Core Configuration */,
				F82C73583A4854BCC70D13CD /* [CP-User] [RNFB] Crashlytics Configuration */,
				218A7451C0024611BD56EA71 /* Upload Debug Symbols to Sentry */,
			);
			buildRules = (
			);
			dependencies = (
				4CA182012D76F8F400E2ED1C /* PBXTargetDependency */,
			);
			name = FonosMobile;
			productName = FonosMobile;
			productReference = 13B07F961A680F5B00A75B9A /* FonosMobile.app */;
			productType = "com.apple.product-type.application";
		};
		4CA181FA2D76F8F400E2ED1C /* NotificationService */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4CA182042D76F8F400E2ED1C /* Build configuration list for PBXNativeTarget "NotificationService" */;
			buildPhases = (
				E1BD9B09F7CBAFD8CDAA709C /* [CP] Check Pods Manifest.lock */,
				4CA181F72D76F8F400E2ED1C /* Sources */,
				4CA181F82D76F8F400E2ED1C /* Frameworks */,
				4CA181F92D76F8F400E2ED1C /* Resources */,
				BB5A8FE5CD6CA0BC9203864D /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationService;
			productName = NotificationService;
			productReference = 4CA181FB2D76F8F400E2ED1C /* NotificationService.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
					4CA181FA2D76F8F400E2ED1C = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "FonosMobile" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* FonosMobile */,
				4CA181FA2D76F8F400E2ED1C /* NotificationService */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81AB9BB82411601600AC10FF /* BootSplash.storyboard in Resources */,
				4CD8F6AD2D7814FF006FA2E6 /* bundles in Resources */,
				4CD8F6882D7707C1006FA2E6 /* Images.xcassets in Resources */,
				4CD8F65F2D770681006FA2E6 /* Localizable.strings in Resources */,
				4CD8F5852D770602006FA2E6 /* Config.xcconfig in Resources */,
				4C7FC69E2D956642004F244D /* Fonos Dev.storekit in Resources */,
				4CD8F6612D770688006FA2E6 /* AppCenter-Config.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				E9F39AD8B687BD69610B0C76 /* PrivacyInfo.xcprivacy in Resources */,
				93F1536C5C19477EACF00C9C /* BebasNeueProBold.otf in Resources */,
				45D24384B50D42A991014C47 /* BebasNeueProRegular.otf in Resources */,
				3BE40940ECE345FBB7D99F4C /* SVN-GilroyBold.otf in Resources */,
				4CD8F6AF2D7AA4BC006FA2E6 /* GoogleService-Info.plist in Resources */,
				FEED52FEE574421DBF17A615 /* SVN-GilroyHeavy.otf in Resources */,
				323099CB8B404646A5A401C5 /* SVN-GilroyLight.otf in Resources */,
				E8EC4BF50C8D4EF3AF7FA45B /* SVN-GilroyMedium.otf in Resources */,
				B3244A6F44A64741962D5576 /* SVN-GilroySemiBold.otf in Resources */,
				6D26B968904A4080BFEE2488 /* EBGaramond.ttf in Resources */,
				939D57266CBD49F5A8B6D8E9 /* LiterataTT.ttf in Resources */,
				5E2BB4D321DA470EB10CD7B1 /* Quicksand.ttf in Resources */,
				C533E618C31A4E7CA625211B /* SVN-GilroyXBold.ttf in Resources */,
				16FFF94411704995BD8CE64D /* LMMonoLt10.ttc in Resources */,
				10906889ADA9433580300F00 /* SVN-TimesNewRoman.ttc in Resources */,
				8D10EFACEA1D44C4B9CB4AE1 /* icomoon.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CA181F92D76F8F400E2ED1C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FonosMobile/Pods-FonosMobile-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FonosMobile/Pods-FonosMobile-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FonosMobile/Pods-FonosMobile-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		218A7451C0024611BD56EA71 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh\n";
		};
		A880DFE9F54D7BDCC1E55B60 /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-FonosMobile/expo-configure-project.sh\"\n";
		};
		BB5A8FE5CD6CA0BC9203864D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NotificationService/Pods-NotificationService-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NotificationService/Pods-NotificationService-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NotificationService/Pods-NotificationService-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C099B964394D7916D3328617 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FonosMobile-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E1BD9B09F7CBAFD8CDAA709C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NotificationService-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FonosMobile/Pods-FonosMobile-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FonosMobile/Pods-FonosMobile-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FonosMobile/Pods-FonosMobile-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F82C73583A4854BCC70D13CD /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
				4CD8F5B42D770668006FA2E6 /* InAppReview.m in Sources */,
				4CD8F5A62D770662006FA2E6 /* RNAppShortcut.m in Sources */,
				4CD8F5A72D770662006FA2E6 /* RNAppShortcutTypes.swift in Sources */,
				4CD8F5AA2D770662006FA2E6 /* AppShortcuts.swift in Sources */,
				4CD8F5BE2D770679006FA2E6 /* SharedCarPlay.swift in Sources */,
				4CD8F5BF2D770679006FA2E6 /* CarPlayManager.swift in Sources */,
				4CD8F61B2D770681006FA2E6 /* LazyImageView.swift in Sources */,
				4CD8F61C2D770681006FA2E6 /* Extensions.swift in Sources */,
				4CD8F61D2D770681006FA2E6 /* FasterImageViewManager.m in Sources */,
				4CD8F61E2D770681006FA2E6 /* Internal.swift in Sources */,
				4CD8F61F2D770681006FA2E6 /* Log.swift in Sources */,
				4CD8F6202D770681006FA2E6 /* ImageTask.swift in Sources */,
				4CD8F6212D770681006FA2E6 /* RNZendesk.swift in Sources */,
				4CD8F6222D770681006FA2E6 /* ImageDecoding.swift in Sources */,
				4CD8F6232D770681006FA2E6 /* ImageProcessors+Anonymous.swift in Sources */,
				4CD8F6812D7707C1006FA2E6 /* RNStoryShare.m in Sources */,
				4CD8F6822D7707C1006FA2E6 /* RNFirebase.m in Sources */,
				4CD8F6832D7707C1006FA2E6 /* RNFirebase.swift in Sources */,
				4CD8F6842D7707C1006FA2E6 /* EpubReader.swift in Sources */,
				4CD8F6852D7707C1006FA2E6 /* RNStoryShare.swift in Sources */,
				4CD8F6862D7707C1006FA2E6 /* EpubReader.m in Sources */,
				4CD8F6872D7707C1006FA2E6 /* RNUtils.m in Sources */,
				4CD8F6242D770681006FA2E6 /* ImageRequest.swift in Sources */,
				4CD8F6252D770681006FA2E6 /* TaskLoadData.swift in Sources */,
				4CD8F6262D770681006FA2E6 /* DataCache.swift in Sources */,
				4CD8F6272D770681006FA2E6 /* DataCaching.swift in Sources */,
				4CD8F6282D770681006FA2E6 /* ImageDecompression.swift in Sources */,
				4CD8F6292D770681006FA2E6 /* FasterImageViewManager.swift in Sources */,
				4CD8F62A2D770681006FA2E6 /* ImageContainer.swift in Sources */,
				4CD8F6702D770769006FA2E6 /* PlayNewCourseIntent.swift in Sources */,
				4CD8F6712D770769006FA2E6 /* AddBookmarkIntent.swift in Sources */,
				4CD8F6722D770769006FA2E6 /* AddFavoriteIntent.swift in Sources */,
				4CD8F6732D770769006FA2E6 /* PlayTopTreningAudioBookIntent.swift in Sources */,
				4CD8F6742D770769006FA2E6 /* PlayNewAudioBookIntent.swift in Sources */,
				4CD8F6752D770769006FA2E6 /* PlayNewEnglishBookIntent.swift in Sources */,
				4CD8F6762D770769006FA2E6 /* PlayTopTrendingCourseIntent.swift in Sources */,
				4CD8F6772D770769006FA2E6 /* SetTimerIntent.swift in Sources */,
				4CD8F62B2D770681006FA2E6 /* ImageProcessing.swift in Sources */,
				4CD8F62C2D770681006FA2E6 /* ImageCaching.swift in Sources */,
				4CD8F62D2D770681006FA2E6 /* UIImage+Extensions.swift in Sources */,
				4CD8F62E2D770681006FA2E6 /* ImageRequestKeys.swift in Sources */,
				4CD8F62F2D770681006FA2E6 /* TaskFetchDecodedImage.swift in Sources */,
				4CD8F6302D770681006FA2E6 /* ImagePipelineConfiguration.swift in Sources */,
				4CD8F6312D770681006FA2E6 /* ImageEncoders+ImageIO.swift in Sources */,
				4CD8F6322D770681006FA2E6 /* ImagePublisher.swift in Sources */,
				4CD8F6332D770681006FA2E6 /* ImagePipelineCache.swift in Sources */,
				4CD8F6342D770681006FA2E6 /* ImageDecoderRegistry.swift in Sources */,
				4CD8F6352D770681006FA2E6 /* RateLimiter.swift in Sources */,
				4CD8F6362D770681006FA2E6 /* RNHeadsetDetect.m in Sources */,
				4CD8F6372D770681006FA2E6 /* ImageProcessors+GaussianBlur.swift in Sources */,
				4CD8F6382D770681006FA2E6 /* ImageProcessors+Composition.swift in Sources */,
				4CD8F6392D770681006FA2E6 /* CachePolicy.swift in Sources */,
				4CD8F63A2D770681006FA2E6 /* DataPublisher.swift in Sources */,
				4CD8F63B2D770681006FA2E6 /* ImageProcessors+Resize.swift in Sources */,
				4CD8F6652D770693006FA2E6 /* PreventScreenCapture.swift in Sources */,
				4CD8F6662D770693006FA2E6 /* PreventScreenCapture.m in Sources */,
				4CD8F63C2D770681006FA2E6 /* ImageEncoding.swift in Sources */,
				4CD8F63D2D770681006FA2E6 /* LinkedList.swift in Sources */,
				4CD8F63E2D770681006FA2E6 /* ImageEncoders+Default.swift in Sources */,
				4CD8F63F2D770681006FA2E6 /* OperationTask.swift in Sources */,
				4CD8F6402D770681006FA2E6 /* Atomic.swift in Sources */,
				4CD8F6412D770681006FA2E6 /* ImageCache.swift in Sources */,
				4CD8F6422D770681006FA2E6 /* TaskLoadImage.swift in Sources */,
				4CD8F6432D770681006FA2E6 /* ImageResponse.swift in Sources */,
				4CD8F6442D770681006FA2E6 /* Graphics.swift in Sources */,
				4CD8F6452D770681006FA2E6 /* ImagePipelineDelegate.swift in Sources */,
				4CD8F6462D770681006FA2E6 /* AsyncImageTask.swift in Sources */,
				4CD8F6472D770681006FA2E6 /* ImagePipelineTask.swift in Sources */,
				4CD8F6482D770681006FA2E6 /* ImageProcessors+Circle.swift in Sources */,
				4CD8F6492D770681006FA2E6 /* ImagePipeline.swift in Sources */,
				4CD8F64A2D770681006FA2E6 /* ImageProcessors+CoreImage.swift in Sources */,
				4CD8F64B2D770681006FA2E6 /* TaskFetchWithPublisher.swift in Sources */,
				4CD8F64C2D770681006FA2E6 /* ImageDecoders+Default.swift in Sources */,
				4CD8F64D2D770681006FA2E6 /* ImageProcessingOptions.swift in Sources */,
				4CD8F64E2D770681006FA2E6 /* ImageProcessors.swift in Sources */,
				4CD8F64F2D770681006FA2E6 /* ImagePipelineError.swift in Sources */,
				4CD8F6502D770681006FA2E6 /* ImageEncoders.swift in Sources */,
				4CD8F6512D770681006FA2E6 /* AssetType.swift in Sources */,
				4CD8F6522D770681006FA2E6 /* ImagePrefetcher.swift in Sources */,
				4CD8F6532D770681006FA2E6 /* DataLoading.swift in Sources */,
				4CD8F6542D770681006FA2E6 /* Priority.swift in Sources */,
				4CD8F6552D770681006FA2E6 /* Cache.swift in Sources */,
				4CD8F6562D770681006FA2E6 /* DataLoader.swift in Sources */,
				4CD8F6572D770681006FA2E6 /* Operation.swift in Sources */,
				4CD8F6582D770681006FA2E6 /* ImageDecoders+Empty.swift in Sources */,
				4CD8F6592D770681006FA2E6 /* ResumableData.swift in Sources */,
				4CD8F65A2D770681006FA2E6 /* TaskFetchOriginalImageData.swift in Sources */,
				4CD8F65B2D770681006FA2E6 /* ResizeMode.swift in Sources */,
				4CD8F65C2D770681006FA2E6 /* RNZendesk.m in Sources */,
				4CD8F65D2D770681006FA2E6 /* AsyncTask.swift in Sources */,
				4CD8F65E2D770681006FA2E6 /* ImageProcessors+RoundedCorners.swift in Sources */,
				4CD8F5C02D770679006FA2E6 /* RNCarPlayTypes.swift in Sources */,
				4CD8F5C12D770679006FA2E6 /* TemplateManager.swift in Sources */,
				4CD8F5C22D770679006FA2E6 /* PhoneSceneDelegate.swift in Sources */,
				4CD8F5C32D770679006FA2E6 /* CarPlaySceneDelegate.swift in Sources */,
				4CD8F5C42D770679006FA2E6 /* RNCarPlay.swift in Sources */,
				4CD8F5C52D770679006FA2E6 /* RNCarPlay.m in Sources */,
				4CD8F5AF2D770662006FA2E6 /* SharedAppShortcut.swift in Sources */,
				4CD8F5B02D770662006FA2E6 /* RNAppShortcut.swift in Sources */,
				AF44132CAD55A3B31CB09994 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CA181F72D76F8F400E2ED1C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4CA1820B2D76F8FE00E2ED1C /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4CA182012D76F8F400E2ED1C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4CA181FA2D76F8F400E2ED1C /* NotificationService */;
			targetProxy = 4CA182002D76F8F400E2ED1C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B4392A12AC88292D35C810B /* Pods-FonosMobile.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FonosMobile/FonosMobile.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = FonosMobile/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.books";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/SkyEpub",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.dev;
				PRODUCT_NAME = FonosMobile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Bridge.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5709B34CF0A7D63546082F79 /* Pods-FonosMobile.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FonosMobile/FonosMobile.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XWWT565B3T;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = FonosMobile/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.books";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/SkyEpub",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.dev;
				PRODUCT_NAME = FonosMobile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Bridge.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		4CA182052D76F8F400E2ED1C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A690D52C2EFDC1C0B1C03D35 /* Pods-NotificationService.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.dev.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4CA182062D76F8F400E2ED1C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 59D6BADC6AD665E62851D83B /* Pods-NotificationService.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.dev.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		4CD8F6B02D7AA63E006FA2E6 /* DebugStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				CCACHE_BINARY = /opt/homebrew/bin/ccache;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				LDPLUSPLUS = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				SWIFT_VERSION = 5.0;
				USE_HERMES = true;
			};
			name = DebugStaging;
		};
		4CD8F6B12D7AA63E006FA2E6 /* DebugStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6B42D7AA813006FA2E6 /* Pods-FonosMobile.debugstaging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FonosMobile/FonosMobile.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = FonosMobile/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.books";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/SkyEpub",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.staging;
				PRODUCT_NAME = FonosMobile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Bridge.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = DebugStaging;
		};
		4CD8F6B22D7AA63E006FA2E6 /* DebugStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6C22D7AB4EE006FA2E6 /* Pods-NotificationService.debugstaging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.staging.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = DebugStaging;
		};
		4CD8F6B62D7AA86B006FA2E6 /* ReleaseStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				CCACHE_BINARY = /opt/homebrew/bin/ccache;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				LDPLUSPLUS = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseStaging;
		};
		4CD8F6B72D7AA86B006FA2E6 /* ReleaseStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6B52D7AA850006FA2E6 /* Pods-FonosMobile.releasestaging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FonosMobile/FonosMobile.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XWWT565B3T;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = FonosMobile/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.books";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/SkyEpub",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.staging;
				PRODUCT_NAME = FonosMobile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Bridge.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = ReleaseStaging;
		};
		4CD8F6B82D7AA86B006FA2E6 /* ReleaseStaging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6C32D7AB4F9006FA2E6 /* Pods-NotificationService.releasestaging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.staging.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = ReleaseStaging;
		};
		4CD8F6BC2D7AB30E006FA2E6 /* DebugProduction */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				CCACHE_BINARY = /opt/homebrew/bin/ccache;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				LDPLUSPLUS = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				SWIFT_VERSION = 5.0;
				USE_HERMES = true;
			};
			name = DebugProduction;
		};
		4CD8F6BD2D7AB30E006FA2E6 /* DebugProduction */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6B92D7AB2AD006FA2E6 /* Pods-FonosMobile.debugproduction.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FonosMobile/FonosMobile.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = FonosMobile/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.books";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/SkyEpub",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile;
				PRODUCT_NAME = FonosMobile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Bridge.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = DebugProduction;
		};
		4CD8F6BE2D7AB30E006FA2E6 /* DebugProduction */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6C42D7AB506006FA2E6 /* Pods-NotificationService.debugproduction.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = DebugProduction;
		};
		4CD8F6BF2D7AB326006FA2E6 /* ReleaseProduction */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				CCACHE_BINARY = /opt/homebrew/bin/ccache;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				LDPLUSPLUS = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseProduction;
		};
		4CD8F6C02D7AB326006FA2E6 /* ReleaseProduction */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6BB2D7AB2E1006FA2E6 /* Pods-FonosMobile.releaseproduction.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FonosMobile/FonosMobile.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XWWT565B3T;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = FonosMobile/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.books";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/SkyEpub",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile;
				PRODUCT_NAME = FonosMobile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Bridge.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = ReleaseProduction;
		};
		4CD8F6C12D7AB326006FA2E6 /* ReleaseProduction */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F6C52D7AB519006FA2E6 /* Pods-NotificationService.releaseproduction.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationService/NotificationService.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = XWWT565B3T;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationService/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationService;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = vn.fonos.mobile.NotificationService;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = ReleaseProduction;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				CCACHE_BINARY = /opt/homebrew/bin/ccache;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				LDPLUSPLUS = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				SWIFT_VERSION = 5.0;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4CD8F5842D770602006FA2E6 /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				CCACHE_BINARY = /opt/homebrew/bin/ccache;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang.sh";
				LDPLUSPLUS = "$(REACT_NATIVE_PATH)/scripts/xcode/ccache-clang++.sh";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "FonosMobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				4CD8F6B12D7AA63E006FA2E6 /* DebugStaging */,
				4CD8F6BD2D7AB30E006FA2E6 /* DebugProduction */,
				13B07F951A680F5B00A75B9A /* Release */,
				4CD8F6B72D7AA86B006FA2E6 /* ReleaseStaging */,
				4CD8F6C02D7AB326006FA2E6 /* ReleaseProduction */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4CA182042D76F8F400E2ED1C /* Build configuration list for PBXNativeTarget "NotificationService" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4CA182052D76F8F400E2ED1C /* Debug */,
				4CD8F6B22D7AA63E006FA2E6 /* DebugStaging */,
				4CD8F6BE2D7AB30E006FA2E6 /* DebugProduction */,
				4CA182062D76F8F400E2ED1C /* Release */,
				4CD8F6B82D7AA86B006FA2E6 /* ReleaseStaging */,
				4CD8F6C12D7AB326006FA2E6 /* ReleaseProduction */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "FonosMobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				4CD8F6B02D7AA63E006FA2E6 /* DebugStaging */,
				4CD8F6BC2D7AB30E006FA2E6 /* DebugProduction */,
				83CBBA211A601CBA00E9B192 /* Release */,
				4CD8F6B62D7AA86B006FA2E6 /* ReleaseStaging */,
				4CD8F6BF2D7AB326006FA2E6 /* ReleaseProduction */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
