<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>vi</string>
	<key>CFBundleDisplayName</key>
	<string>$(IOS_APP_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundleAlternateIcons</key>
		<dict>
			<key>TetIcon</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>TetIcon</string>
				</array>
				<key>UIPrerenderedIcon</key>
				<false/>
			</dict>
		</dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon</string>
			</array>
			<key>CFBundleIconName</key>
			<string>AppIcon</string>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
	</dict>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(IOS_APP_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.1.4</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(FB_URL_SCHEME)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(FIREBASE_REVERSED_CLIENT_ID)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bundle ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(APP_ID)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(APP_SCHEME)</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>**********</string>
	<key>CleverTapAccountID</key>
	<string>$(CLEVERTAP_ACCOUNT_ID)</string>
	<key>CleverTapRegion</key>
	<string>$(CLEVERTAP_REGION)</string>
	<key>CleverTapToken</key>
	<string>$(CLEVERTAP_TOKEN)</string>
	<key>CodePushDeploymentKey</key>
	<string>$(IOS_CODEPUSH_DEPLOYMENT_KEY)</string>
	<key>FacebookAppID</key>
	<string>$(FB_APP_ID)</string>
	<key>FacebookClientToken</key>
	<string>$(FB_CLIENT_TOKEN)</string>
	<key>FacebookDisplayName</key>
	<string>$(FB_APP_NAME)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>message</string>
		<string>readdle-spark</string>
		<string>airmail</string>
		<string>ms-outlook</string>
		<string>googlegmail</string>
		<string>inbox-gmail</string>
		<string>instagram</string>
		<string>fb</string>
		<string>facebook</string>
		<string>twitter</string>
		<string>viber</string>
		<string>whatsapp</string>
		<string>wechat</string>
		<string>line</string>
		<string>instagram-stories</string>
		<string>facebook-stories</string>
		<string>fb-messenger</string>
		<string>kakaotalk</string>
		<string>mqq</string>
		<string>vk</string>
		<string>comgooglemaps</string>
		<string>googlephotos</string>
		<string>ha</string>
		<string>yammer</string>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAdvertisingAttributionReportEndpoint</key>
	<string>https://appsflyer-skadnetwork.com/</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>To scan qrcode or take a picture for your avatar, we need to access your camera</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>For push notification, we need to access your location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>For push notification, we need to access your location.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>For bug report, we need to access your microphone.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>To scan qrcode or download photo, we need permission to access your library</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To attach a photo to bug reports, we need to access your photo library.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Your data will be used to create a customized experience to your interests</string>
	<key>OneSignal_disable_badge_clearing</key>
	<true/>
	<key>OneSignal_suppress_launch_urls</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>SVN-GilroyBold.otf</string>
		<string>SVN-GilroySemiBold.otf</string>
		<string>SVN-GilroyLight.otf</string>
		<string>SVN-GilroyMedium.otf</string>
		<string>EBGaramond.ttf</string>
		<string>Quicksand.ttf</string>
		<string>LiterataTT.ttf</string>
		<string>LMMonoLt10.ttc</string>
		<string>SVN-TimesNewRoman.ttc</string>
		<string>BebasNeueProRegular.otf</string>
		<string>BebasNeueProBold.otf</string>
		<string>SVN-GilroyHeavy.otf</string>
		<string>SVN-GilroyXBold.ttf</string>
		<string>icomoon.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<true/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>CPTemplateApplicationSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneClassName</key>
					<string>CPTemplateApplicationScene</string>
					<key>UISceneConfigurationName</key>
					<string>CarPlaySceneConfiguration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).CarPlaySceneDelegate</string>
				</dict>
			</array>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneClassName</key>
					<string>UIWindowScene</string>
					<key>UISceneConfigurationName</key>
					<string>PhoneSceneConfiguration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).PhoneSceneDelegate</string>
					<key>UISceneStoryboardFile</key>
					<string>BootSplash</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
