<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>aps-environment</key>
	<string>development</string>
	<key>com.apple.developer.applesignin</key>
	<array>
		<string>Default</string>
	</array>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:fonosdev.page.link</string>
		<string>applinks:fonosstaging.page.link</string>
		<string>applinks:fonos.page.link</string>
		<string>applinks:fonos-dev.onelink.me</string>
		<string>applinks:fonos.onelink.me</string>
		<string>applinks:fonos-staging.onelink.me</string>
		<string>applinks:app-dev.fonos.dev</string>
		<string>applinks:app-staging.fonos.dev</string>
		<string>applinks:go.fonos.app</string>
	</array>
	<key>com.apple.developer.carplay-audio</key>
	<true/>
	<key>com.apple.developer.devicecheck.appattest-environment</key>
	<string>production</string>
	<key>com.apple.security.application-groups</key>
	<array>
		<string>group.vn.fonos.mobile.dev.onesignal</string>
		<string>group.vn.fonos.mobile.onesignal</string>
		<string>group.vn.fonos.mobile.staging.onesignal</string>
	</array>
	<key>inter-app-audio</key>
	<true/>
</dict>
</plist>
