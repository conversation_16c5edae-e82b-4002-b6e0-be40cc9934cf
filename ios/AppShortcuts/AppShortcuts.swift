//
//  PlayAudioBookShocurt.swift
//  FonosMobile
//
//  Created by <PERSON>  on 16/1/25.
//  Copyright © 2025 Facebook. All rights reserved.
//

import Foundation
import AppIntents


@available(iOS 16, *)
struct AppShortcuts: AppShortcutsProvider {
  static var appShortcuts: [AppShortcut] {
    AppShortcut(
      intent: PlayNewAudioBookIntent(),
      phrases: [
        "Play the newest audiobook in \(.applicationName)",
        "Run the newest audiobook in \(.applicationName)",
        "Open the newest audiobook in \(.applicationName)",
        "Mở sách nói mới nhất trên \(.applicationName)"
      ],
      shortTitle: "Sách nói mới nhất",
      systemImageName: "play.circle"
    )
    AppShortcut(
      intent: PlayTopTreningAudioBookIntent(),
      phrases: [
        "Play the top trending audiobook in \(.applicationName)",
        "Run the top trending audiobook in \(.applicationName)",
        "Open the top trending audiobook in \(.applicationName)",
        "Mở sách nói thịnh hành nhất trên \(.applicationName)"
      ],
      shortTitle: "Sách nói thịnh hành nhất",
      systemImageName: "play.circle"
    )
    AppShortcut(
      intent: PlayNewCourseIntent(),
      phrases: [
        "Play the newest podcourse in \(.applicationName)",
        "Run the newest podcourse in \(.applicationName)",
        "Open the newest podcourse in \(.applicationName)",
        "Mở khóa học mới nhất trên \(.applicationName)",
        "Mở podcourse mới nhất trên \(.applicationName)"
      ],
      shortTitle: "Podcourse mới nhất",
      systemImageName: "play.square"
    )
    AppShortcut(
      intent: PlayTopTrendingCourseIntent(),
      phrases: [
        "Play the top trending course in \(.applicationName)",
        "Run the top trending course in \(.applicationName)",
        "Open the top trending course in \(.applicationName)",
        "Mở khóa học thịnh hành nhất trên \(.applicationName)",
        "Mở podcourse thịnh hành nhất trên \(.applicationName)"
      ],
      shortTitle: "Podcourse thịnh hành nhất",
      systemImageName: "play.square"
    )
    AppShortcut(
      intent: PlayNewEnglishBookIntent(),
      phrases: [
        "Play the newest english in \(.applicationName)",
        "Run the newest english in \(.applicationName)",
        "Open the newest english in \(.applicationName)",
        "Mở sách tiếng anh mới nhất trên \(.applicationName)",
        "Mở english mới nhất trên \(.applicationName)",
      ],
      shortTitle: "English mới nhất",
      systemImageName: "play.circle.fill"
    )
    AppShortcut(
      intent: AddFavoriteIntent(),
      phrases: [
        "I like this content in \(.applicationName)",
        "Add this content to my library in \(.applicationName)",
        "Add this content to my favorite in \(.applicationName)",
        "Tôi thích nội dung này trên \(.applicationName)",
        "Thêm nội dung này vào mục yêu thích trên \(.applicationName)"
      ],
      shortTitle: "Thêm yêu thích",
      systemImageName: "heart.text.square.fill"
    )
    AppShortcut(
      intent: AddBookmarkIntent(),
      phrases: [
        "Add bookmark to this content in \(.applicationName)",
        "Thêm đánh dấu cho nội dung này trên \(.applicationName)",
      ],
      shortTitle: "Thêm đánh dấu",
      systemImageName: "bookmark"
    )
    AppShortcut(
      intent: SetTimerIntent(),
      phrases: [
        "Set a sleep timer for 30 minutes in \(.applicationName)",
        "Hẹn giờ nghỉ sau 30 phút trên \(.applicationName)",
        "Tự động ngừng nghe nội dung này sau 30 phút trên \(.applicationName)"
      ],
      shortTitle: "Ngừng sau 30 phút",
      systemImageName: "moon.zzz"
    )
    

  }
}





