//
//  SharedCarPlay.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 16/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

import Foundation

@objc
class SharedAppShortcut: NSObject {
  static let instance = SharedAppShortcut()
  var existNewestBookData: Bool? = false
  var existNewestCourseData: Bool? = false
  var existNewestEnglishData: Bool? = false
  var existTrendingBookData: Bool? = false
  var existTrendingCourseData: Bool? = false
  var isPlayerPlaying : Bool? = false
  var hasSubcription : Bool? = false
  var hasPurchased: Bool? = false
  @objc dynamic var state: String = RNAppShortcutState.None.rawValue
  
  static func setNewestBookData(isExist: Bool) {
    SharedAppShortcut.instance.existNewestBookData = isExist
  }
  
  static func existNewestBookData() -> Bool {
    return SharedAppShortcut.instance.existNewestBookData!
  }
  
  static func setNewestCourseData(isExist: Bool) {
    SharedAppShortcut.instance.existNewestCourseData = isExist
  }
  
  static func existNewestCourseData() -> Bool {
    return SharedAppShortcut.instance.existNewestCourseData!
  }
  
  static func setNewestEnglishData(isExist: Bool) {
    SharedAppShortcut.instance.existNewestEnglishData = isExist
  }
  
  static func existNewestEnglishData() -> Bool {
    return SharedAppShortcut.instance.existNewestEnglishData!
  }
  
  static func setTrendingBookData(isExist: Bool) {
    SharedAppShortcut.instance.existTrendingBookData = isExist
  }
  
  static func existTrendingBookData() -> Bool {
    return SharedAppShortcut.instance.existTrendingBookData!
  }
  
  static func setTrendingCourseData(isExist: Bool) {
    SharedAppShortcut.instance.existTrendingCourseData = isExist
  }
  
  static func existTrendingCourseData() -> Bool {
    return SharedAppShortcut.instance.existTrendingCourseData!
  }
  
  
  static func setState(value: RNAppShortcutState) {
    SharedAppShortcut.instance.state = value.rawValue
  }
  
  static func getState() -> String {
    return SharedAppShortcut.instance.state
  }
  
  static func setPlayerPlaying(isPlaying: Bool) {
    SharedAppShortcut.instance.isPlayerPlaying = isPlaying
  }
  
  static func isPlayerPlaying() -> Bool {
    return SharedAppShortcut.instance.isPlayerPlaying!
  }
  
  static func setHasSubscription(hasSubscription: Bool) {
    SharedAppShortcut.instance.hasSubcription = hasSubscription
  }
  
  static func hasSucription() -> Bool {
    return SharedAppShortcut.instance.hasSubcription!
  }
  
  static func setPurchased(hasPurchased: Bool) {
    SharedAppShortcut.instance.hasPurchased = hasPurchased
  }
  
  static func hasPurchased() -> Bool {
    return SharedAppShortcut.instance.hasPurchased!
  }
}
