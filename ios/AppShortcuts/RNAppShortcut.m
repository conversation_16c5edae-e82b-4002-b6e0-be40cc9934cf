//
//  RNAppShortcut.m
//  FonosMobile
//
//  Created by <PERSON>  on 20/1/25.
//  Copyright © 2025 Facebook. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>

@interface RCT_EXTERN_MODULE(RNAppShortcut, RCTEventEmitter)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

+ (BOOL)requiresMainQueueSetup
{
    return YES;
}

RCT_EXTERN_METHOD(setAppShortcutData: (NSDictionary *)data)
RCT_EXTERN_METHOD(setState: (NSString *)state)
RCT_EXTERN_METHOD(setPlayerPlaying:(BOOL)isPlay)
RCT_EXTERN_METHOD(setHasSubscription:(BOOL)hasSubscription)
RCT_EXTERN_METHOD(setPurchased:(BOOL)hasPurchased)
@end
