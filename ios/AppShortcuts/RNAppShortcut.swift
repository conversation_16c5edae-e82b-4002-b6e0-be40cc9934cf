//
//  RNCarPlay.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 14/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//
import Foundation
import React

@available(iOS 14.0, *)
@objc (RNAppShortcut)
class RNAppShortcut: RCTEventEmitter {
  public static var shared: RNAppShortcut?
  
  override init() {
    super.init()
    RNAppShortcut.shared = self
  }

  
  open override func supportedEvents() -> [String] { [
    RNAppShortcutEvent.PlayNewestAudiobook.rawValue,
    RNAppShortcutEvent.PlayNewestCourse.rawValue,
    RNAppShortcutEvent.PlayNewestEnglish.rawValue,
    RNAppShortcutEvent.PlayTrendingBook.rawValue,
    RNAppShortcutEvent.PlayTrendingCourse.rawValue,
    RNAppShortcutEvent.AddBookmark.rawValue,
    RNAppShortcutEvent.AddFavorite.rawValue,
    RNAppShortcutEvent.SetTimer30Minutes.rawValue,
  ] }
 
  @objc
  public func setAppShortcutData(_ data: NSDictionary) {
    SharedAppShortcut.setNewestBookData(isExist: data["newestBook"] as! Bool)
    SharedAppShortcut.setNewestCourseData(isExist: data["newestCourse"] as! Bool)
    SharedAppShortcut.setNewestEnglishData(isExist: data["newestEnglish"] as! Bool)
    SharedAppShortcut.setTrendingBookData(isExist: data["trendingBook"] as! Bool)
    SharedAppShortcut.setTrendingCourseData(isExist: data["trendingCourse"] as! Bool)
  }
  
  @objc
  public func setState(_ state: String) {
    SharedAppShortcut.setState(value: RNAppShortcutState(rawValue: state)!)
  }
  
  @objc
  public func setPlayerPlaying(_ isPlay: Bool) {
    SharedAppShortcut.setPlayerPlaying(isPlaying: isPlay)
  }
  
  @objc
  public func setHasSubscription(_ hasSubscription: Bool) {
    SharedAppShortcut.setHasSubscription(hasSubscription: hasSubscription)
  }
  
  
  @objc
  public func setPurchased(_ hasPurchased: Bool) {
    SharedAppShortcut.setPurchased(hasPurchased: hasPurchased)
  }
  
  
  static public func emitEvent(withName name: RNAppShortcutEvent, body: Any!) {
    if (RNAppShortcut.shared != nil) {
      RNAppShortcut.shared!.sendEvent(withName: name.rawValue, body:body)
    }
  }
}

