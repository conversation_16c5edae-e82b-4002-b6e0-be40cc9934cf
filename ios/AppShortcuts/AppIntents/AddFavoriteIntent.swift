import AppIntents
import Foundation

@available(iOS 16, *)
struct AddFavoriteIntent: AppIntent {
  static let title: LocalizedStringResource = "Thêm nội dung này vào mục yêu thích"

  func perform() -> some IntentResult & ProvidesDialog {
    if SharedAppShortcut.isPlayerPlaying() {
      RNAppShortcut.emitEvent(withName: RNAppShortcutEvent.AddFavorite, body: true)
      return .result(dialog: "Đã thêm nội dung này vào mục yêu thích.")
    } else {
      return .result(dialog: "Không tìm thấy nội dung để thêm vào mục yêu thích.")
    }
  }
}
