import Foundation
import AppIntents


@available(iOS 16, *)
struct SetTimerIntent: AppIntent {
    static let title: LocalizedStringResource = "Hẹn thời gian nghỉ sau 30 phút"
  
    func perform() -> some IntentResult & ProvidesDialog {
      if (SharedAppShortcut.isPlayerPlaying()){
        RNAppShortcut.emitEvent(withName: RNAppShortcutEvent.SetTimer30Minutes, body:true )
        return .result(dialog: "Hẹn thời gian nghỉ thành công.")
      } else {
        return .result(dialog: "Không tìm thấy nội dung để thêm hẹn thời gian nghỉ.")
      }
    }
}
