import Foundation
import AppIntents

@available(iOS 16, *)
struct PlayNewCourseIntent: AppIntent {
    static let title: LocalizedStringResource = "Mở podcourse mới nhất"
   static var openAppWhenRun: Bool = true
  
    func perform() -> some IntentResult & ProvidesDialog {
        if (SharedAppShortcut.getState() == RNAppShortcutState.None.rawValue){
          var rerentObserve: NSKeyValueObservation?
           rerentObserve = SharedAppShortcut.instance.observe(\.state, options: .new) { sharedInstance, _change in
            if (SharedAppShortcut.getState() != RNAppShortcutState.None.rawValue){
              if (SharedAppShortcut.existNewestCourseData()){
                RNAppShortcut.emitEvent(withName: RNAppShortcutEvent.PlayNewestCourse, body:true )
                rerentObserve?.invalidate()
                rerentObserve = nil
              }
            }
          }
        } else {
          if (SharedAppShortcut.existNewestCourseData()){
            RNAppShortcut.emitEvent(withName: RNAppShortcutEvent.PlayNewestCourse, body:true )
          } else {
            return .result(dialog: "Không tìm thấy nội dung phù hợp trên Fonos.")
          }
        }
        return .result(dialog: "Đang mở podcourse mới nhất trên Fonos, vui lòng đợi trong giây lát.")
    }
}
