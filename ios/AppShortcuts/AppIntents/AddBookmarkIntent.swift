import Foundation
import AppIntents


@available(iOS 16, *)
struct AddBookmarkIntent: AppIntent {
    static let title: LocalizedStringResource = "Thêm đánh dấu cho nội dung đang nghe"
  
    func perform() -> some IntentResult & ProvidesDialog {
      if (SharedAppShortcut.isPlayerPlaying() ){
        if (SharedAppShortcut.hasSucription() && SharedAppShortcut.hasPurchased()){
          RNAppShortcut.emitEvent(withName: RNAppShortcutEvent.AddBookmark, body:true )
          return .result(dialog: "Thêm đánh dấu thành công.")
        } else if (!SharedAppShortcut.hasPurchased()) {
          return .result(dialog: "Bạn không sở hữu quyển sách này.")
        } else if (!SharedAppShortcut.hasSucription()) {
          return .result(dialog: "Bạn đã hết hạn gói hội viên.")
        } else {
          return .result(dialog: "Không tìm thấy nội dung để thêm đánh dấu.")
        }
      } else {
        return .result(dialog: "Không tìm thấy nội dung để thêm đánh dấu.")
      }
    }
}
