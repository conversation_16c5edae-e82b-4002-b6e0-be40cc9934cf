//
//  RNAppShortcutTypes.swift
//  FonosMobile
//
//  Created by <PERSON>  on 20/1/25.
//  Copyright © 2025 Facebook. All rights reserved.
//

import Foundation

enum RNAppShortcutState: String {
  case Anonymous, Authenticated, None
}


enum RNAppShortcutEvent: String {
  case PlayNewestAudiobook = "@appshortcut:play:newest:book"
  case PlayNewestCourse = "@appshortcut:play:newest:course"
  case PlayNewestEnglish = "@appshortcut:play:newest:english"
  case PlayTrendingBook = "@appshortcut:play:trending:book"
  case PlayTrendingCourse = "@appshortcut:play:trending:course"
  case AddFavorite = "@appshortcut:add:favorite"
  case AddBookmark = "@appshortcut:add:bookmark"
  case SetTimer30Minutes = "@appshortcut:set:timer"
}
