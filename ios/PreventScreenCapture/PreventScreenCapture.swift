//
//  PreventScreenCapture.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON> on 18/01/2024.
//  Copyright © 2024 Facebook. All rights reserved.
//

import Foundation
import React

@objc(PreventScreenCaptureManager)
class PreventScreenCaptureManager: RCTViewManager {
  override static func requiresMainQueueSetup() -> Bool {
    return true
  }
  
  override func view() -> UIView! {
    return PreventScreenCapture()
  }
}

@objc(PreventScreenCapture)
class PreventScreenCapture: UIView {
  init() {
    super.init(frame: .zero)
    self.observeScreenCapture()
  }
  
  required init?(coder: NSCoder) {
    super.init(coder: coder)
  }
  
  var timer: Timer!
  var shouldPreventCapture: Bool!
  
  @objc var onCapturing: RCTDirectEventBlock?
  
  @objc var enabled = false {
    didSet {
      if enabled {
        self.shouldPreventCapture = true
        self.preventCaptureScreen()
      } else {
        self.shouldPreventCapture = false
      }
    }
  }
  
  @objc func captureScreen(_ notification: Notification) {
    if self.shouldPreventCapture {
      self.preventCaptureScreen()
    }
  }
  
  func observeScreenCapture() {
    NotificationCenter.default.addObserver(self,
                                           selector: #selector(captureScreen(_:)),
                                           name:  UIScreen.capturedDidChangeNotification,
                                           object: nil)
  
  }
  
  
  
  
  @objc func preventCaptureScreen() {
    let alertController = UIAlertController(title: "Screen recording not allowed!", message: "Please turn off screen recording to continue", preferredStyle: .alert)
    
    if (UIScreen.main.isCaptured) {
      self.onCapturing?([
        "capturing": true
      ])
      // Present the alert controller
      self.window?.rootViewController?.present(alertController, animated: true, completion: nil)
      
      // Create a full-screen view
      let colourView = UIView()
      if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
        colourView.frame = window.bounds
      } else {
        colourView.frame = UIScreen.main.bounds
      }
      colourView.backgroundColor = .white
      colourView.alpha = 0
      
      // Add the view to the window
      if let window = self.window {
        window.addSubview(colourView)
        window.bringSubviewToFront(colourView)
      } else {
        self.addSubview(colourView)
        self.bringSubviewToFront(colourView)
      }
      
      // Animate the view to be visible
      UIView.animate(withDuration: 0.5) {
        colourView.alpha = 1
      }
      
      timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { timer in
        if UIScreen.main.isCaptured == false {
          self.onCapturing?([
            "capturing": false
          ])
          
          self.timer.invalidate()
          
          UIView.animate(withDuration: 0.5, animations: {
            colourView.alpha = 0
          }, completion: {_ in
            colourView.removeFromSuperview()
            alertController.dismiss(animated: true, completion: nil)
          })
        }
      }
    }
  }
}
