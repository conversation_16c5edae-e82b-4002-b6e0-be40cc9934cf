//
//  RNCarPlay.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 14/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//
import Foundation
import CarPlay
import React

@available(iOS 14.0, *)
@objc (RNCarPlay)
class RNCarPlay: RCTEventEmitter {
  public static var shared: RNCarPlay?
  
  override init() {
    super.init()
    RNCarPlay.shared = self
  }

  
  open override func supportedEvents() -> [String] { [
    RNCarPlayEvent.DidConnect.rawValue,
    RNCarPlayEvent.DidDisConnect.rawValue,
    RNCarPlayEvent.Play.rawValue,
    RNCarPlayEvent.MoveToTrack.rawValue,
    RNCarPlayEvent.MoveToPrevious.rawValue,
    RNCarPlayEvent.SetPlaybackRate.rawValue,
    RNCarPlayEvent.PhoneAppOpen.rawValue,
  ] }
 
  @objc
  public func setRecentData(_ data: [NSDictionary]) {
    SharedCarPlay.setRecentData(data: data)
  }
  
  @objc
  public func setLibraryData(_ data: [NSDictionary]) {
    SharedCarPlay.setLibraryData(data: data)
  }
  
  @objc
  public func setTracks(_ data: [NSDictionary]) {
    SharedCarPlay.setTracks(data: data)
  }
  
  @objc
  public func setState(_ state: String) {
    SharedCarPlay.setState(value: RNCarPlayState(rawValue: state)!)
  }
  
  @objc
  public func setCurrentTrackIndex(_ value: NSInteger) {
    if (!SharedCarPlay.isBlockUpdateFromJs()) {
      SharedCarPlay.setCurrentTrackIndex(value: value)
    }
  }
  
  @objc
  public func setPlaybackRate(_ value: CGFloat) {
    if (!SharedCarPlay.isBlockUpdateFromJs()) {
      SharedCarPlay.setPlaybackRate(value: value)
    }
  }
  
  static public func emitEvent(withName name: RNCarPlayEvent, body: Any!) {
    if (RNCarPlay.shared != nil) {
      RNCarPlay.shared!.sendEvent(withName: name.rawValue, body:body)
    }
  }
  
  static public func didPhoneActive() {
    RNCarPlay.emitEvent(withName: RNCarPlayEvent.PhoneAppOpen, body:true )
  }
  
  static public func didPhoneDeActive() {
    RNCarPlay.emitEvent(withName: RNCarPlayEvent.PhoneAppOpen, body:false )
  }
 
  static public func didConnect() {
    SharedCarPlay.setIsConnected(value: true)
    RNCarPlay.emitEvent(withName: RNCarPlayEvent.DidConnect, body:true )
  }
  
  static public func didDisconnect() {
    SharedCarPlay.setIsConnected(value: false)
    RNCarPlay.emitEvent(withName: RNCarPlayEvent.DidDisConnect, body:true )
  }
  
  @objc
  public func checkConnection() {
    if(SharedCarPlay.instance.isConnected) {
      RNCarPlay.didConnect()
    }
  }
  
  @objc
  public func navigateToNowPlaying() {
    if((SharedCarPlay.instance.interfaceController) != nil) {
      let templates = SharedCarPlay.instance.interfaceController.templates
      var existNowPlayingTemplate = false
      for templateItem in templates {
        if (templateItem.isKind(of: CPNowPlayingTemplate.self)){
          existNowPlayingTemplate = true
        }
      }
      if (!existNowPlayingTemplate && SharedCarPlay.instance.isInitRootTemplate){
        SharedCarPlay.instance.interfaceController!.pushTemplate(CPNowPlayingTemplate.shared, animated: true, completion: nil)
      }
      SharedCarPlay.setIsWaitingInitPlayer(isWaiting: false)
    }
  }
}

