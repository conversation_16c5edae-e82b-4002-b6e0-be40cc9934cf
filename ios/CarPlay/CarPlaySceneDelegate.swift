//
//  TemplateScreenDelegate.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON> on 30/03/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

import Foundation
import CarPlay
import UIKit

/// `TemplateApplicationSceneDelegate` is the UIScenDelegate and CPTemplateApplicationSceneDelegate.
@available(iOS 14.0, *)
class CarPlaySceneDelegate: NSObject {
    
    /// The template manager handles the connection to CarPlay and manages the displayed templates.
    public let carplayManager = CarPlayManager()
    // MARK: UISceneDelegate
    
    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        if scene is CPTemplateApplicationScene, session.configuration.name == "CarPlaySceneConfiguration" {
            print("Template application scene will connect.")
         
        }
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        if scene.session.configuration.name == "CarPlaySceneConfiguration" {
            print("Template application scene did disconnect.")
        }
    }
    
    func sceneDidBecomeActive(_ scene: UIScene) {
        if scene.session.configuration.name == "CarPlaySceneConfiguration" {
            print("Template application scene did become active.")
        }
    }
    
    func sceneWillResignActive(_ scene: UIScene) {
        if scene.session.configuration.name == "CarPlaySceneConfiguration" {
            print("Template application scene will resign active.")
        }
    }
}

// MARK: CPTemplateApplicationSceneDelegate

@available(iOS 14.0, *)
extension CarPlaySceneDelegate: CPTemplateApplicationSceneDelegate {
    
    func templateApplicationScene(_ templateApplicationScene: CPTemplateApplicationScene, didConnect interfaceController: CPInterfaceController) {
      print("Template application scene did connect.")
      carplayManager.connect(interfaceController)
      RNCarPlay.didConnect()
      SharedCarPlay.setInterfaceController(controller: interfaceController)
    }
    
    func templateApplicationScene(_ templateApplicationScene: CPTemplateApplicationScene,
                                  didDisconnectInterfaceController interfaceController: CPInterfaceController) {
      print("Template application scene did disconnect.")
      carplayManager.disconnect()
      RNCarPlay.didDisconnect()
    }
}
