import A<PERSON>FlyerLib
import FBAEMKit
import FBSDKCoreKit
import FirebaseAuth
import Foundation
import RNBootSplash
import SwiftUI
import UIKit
import react_native_config

class PhoneSceneDelegate: UIResponder, UIWindowSceneDelegate {
  var window: UIWindow?
  var rootViewController: UIViewController!
  var requestOpenUrlLink: URL?
  func scene(
    _ scene: UIScene, willConnectTo session: UISceneSession,
    options connectionOptions: UIScene.ConnectionOptions
  ) {
    //Universial Link Open when app is start.
    if let userActivity = connectionOptions.userActivities.first,
      userActivity.activityType == NSUserActivityTypeBrowsingWeb,
      let url = userActivity.webpageURL
    {
      requestOpenUrlLink = url
    }

    //Deeplink Open when app is start.
    if connectionOptions.urlContexts.first?.url != nil {
      let url = connectionOptions.urlContexts.first?.url
      requestOpenUrlLink = url!
    }

    if connectionOptions.notificationResponse?.notification.request.content != nil {
      let userInfo = connectionOptions.notificationResponse?.notification.request.content.userInfo
      if let customValue = userInfo!["wzrk_dl"] as? String {
        requestOpenUrlLink = URL(string: customValue)
      }
    }

    if let userActivity = connectionOptions.userActivities.first,
      let url = userActivity.webpageURL,
      userActivity.activityType == NSUserActivityTypeBrowsingWeb
    {
      let appId = RNCConfig.env(for: "FB_APP_ID")!
      AEMReporter.configure(networker: nil, appID: appId, reporter: nil)
      AEMReporter.enable()
      AEMReporter.handle(url)
    }

    if scene.session.configuration.name == "PhoneSceneConfiguration"
      && SharedCarPlay.instance.isPhoneConnected == false
    {
      guard let appDelegate = (UIApplication.shared.delegate as? AppDelegate) else { return }
      guard let windowScene = (scene as? UIWindowScene) else { return }

      // Create React Native view using centralized helper
      guard let phoneRootView = appDelegate.createReactNativeView() else { return }

      // Set up view controller and window
      rootViewController = UIViewController()
      rootViewController.view = phoneRootView

      let window = UIWindow(windowScene: windowScene)
      window.rootViewController = rootViewController
      self.window = window
      RNBootSplash.initWithStoryboard("BootSplash", rootView: phoneRootView)
      window.makeKeyAndVisible()
      SharedCarPlay.setIsPhoneConnected(value: true)
    }
  }

  func sceneDidDisconnect(_ scene: UIScene) {
    if scene.session.configuration.name == "PhoneSceneConfiguration" {
      SharedCarPlay.setIsPhoneConnected(value: false)
    }
  }

  func sceneDidBecomeActive(_ scene: UIScene) {
    if scene.session.configuration.name == "PhoneSceneConfiguration" {
      DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
        if #available(iOS 14.0, *) {
          RNCarPlay.didPhoneActive()
        }

        if self.requestOpenUrlLink != nil {
          AppsFlyerLib.shared().handleOpen(self.requestOpenUrlLink!)
          self.requestOpenUrlLink = nil
        }
      }
    }
  }

  func sceneWillEnterForeground(_ scene: UIScene) {
    //    SharedCarPlay.setIsPhoneConnected(value: true)
  }

  func sceneWillResignActive(_ scene: UIScene) {
    if scene.session.configuration.name == "PhoneSceneConfiguration" {
    }

  }

  // Universial link Open when app is onPause
  func scene(_ scene: UIScene, continue userActivity: NSUserActivity) {
    if userActivity.activityType == NSUserActivityTypeBrowsingWeb,
      let urlinfo = userActivity.webpageURL
    {
      AppsFlyerLib.shared().handleOpen(urlinfo)
    }

    if let url = userActivity.webpageURL,
      userActivity.activityType == NSUserActivityTypeBrowsingWeb
    {
      let appId = RNCConfig.env(for: "FB_APP_ID")!
      AEMReporter.configure(networker: nil, appID: appId, reporter: nil)
      AEMReporter.enable()
      AEMReporter.handle(url)
    }
  }

  // Deeplink Open when app in onPause
  func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
    let url = URLContexts.first!.url as URL
    Auth.auth().canHandle(url)
    AppsFlyerLib.shared().handleOpen(url)
    ApplicationDelegate.shared.application(
      UIApplication.shared,
      open: url,
      sourceApplication: nil,
      annotation: [UIApplication.OpenURLOptionsKey.annotation]
    )

    let appId = RNCConfig.env(for: "FB_APP_ID")!
    AEMReporter.configure(networker: nil, appID: appId, reporter: nil)
    AEMReporter.enable()
    AEMReporter.handle(url)
  }
}
