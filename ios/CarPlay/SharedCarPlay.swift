//
//  SharedCarPlay.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 16/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

import Foundation
import CarPlay

@objc
class SharedCarPlay: NSObject {
  static let instance = SharedCarPlay()
  var isConnected: Bool = false
  @objc var isPhoneConnected: Bool = false
  var shouldBlockUpdateFromJS: Bool! = false
  var interfaceController: CPInterfaceController!
  var isInitRootTemplate: Bool! = false
  var closureItemPress: (() -> Void)?
  @objc dynamic var isWaitingInitPlayer: Bool = false
  @objc dynamic var recentData: [NSDictionary] = []
  @objc dynamic var libraryData: [NSDictionary] = []
  @objc dynamic var state: String = RNCarPlayState.Connecting.rawValue
  
  @objc dynamic var currentTrackIndex: NSInteger = -1
  @objc dynamic var playbackRate: CGFloat = 1
  
  var tracks: [NSDictionary] = []

  static func setRecentData(data: [NSDictionary]) {
    SharedCarPlay.instance.recentData = data
  }
  
  static func setLibraryData(data: [NSDictionary]) {
    SharedCarPlay.instance.libraryData = data
  }
  
  static func setCurrentTrackIndex(value: NSInteger) {
    SharedCarPlay.instance.currentTrackIndex = value
  }
  
  static func setTracks(data: [NSDictionary]?) {
    if (data != nil) {
      SharedCarPlay.instance.tracks = data!
    } else {
      SharedCarPlay.instance.tracks = []
    }
   
  }
  
  static func setState(value: RNCarPlayState) {
    SharedCarPlay.instance.state = value.rawValue
  }
  
  static func getRecentData() -> [NSDictionary] {
    return SharedCarPlay.instance.recentData
  }
  
  static func getLibraryData() -> [NSDictionary] {
    return SharedCarPlay.instance.libraryData
  }
  
  static func getCurrentTrackIndex() -> NSInteger {
    return SharedCarPlay.instance.currentTrackIndex
  }
  
  static func getPlaybackRate() -> CGFloat {
    return SharedCarPlay.instance.playbackRate
  }
  
  static func setPlaybackRate(value: CGFloat) {
    SharedCarPlay.instance.playbackRate = value
  }
  
  
  static func setIsConnected(value: Bool) {
    return SharedCarPlay.instance.isConnected = value
  }
  
  static func setIsPhoneConnected(value: Bool) {
    return SharedCarPlay.instance.isPhoneConnected = value
  }
  
  static func getTracks() -> [NSDictionary] {
    return SharedCarPlay.instance.tracks
  }
  
  static func getState() -> RNCarPlayState {
    return RNCarPlayState(rawValue: SharedCarPlay.instance.state) ?? RNCarPlayState.Connecting
  }
  
  static func isBlockUpdateFromJs() -> Bool {
    return SharedCarPlay.instance.shouldBlockUpdateFromJS
  }
  
  static func setInterfaceController(controller: CPInterfaceController) {
    SharedCarPlay.instance.interfaceController = controller
  }
  
  
  static func setIsWaitingInitPlayer(isWaiting: Bool) {
    SharedCarPlay.instance.isWaitingInitPlayer = isWaiting
  }
  
  static func setInitRootTemplate(initRoot: Bool) {
    SharedCarPlay.instance.isInitRootTemplate = initRoot
  }
  
  @objc
  static func getIsPhoneConnected() -> Bool {
    return SharedCarPlay.instance.isPhoneConnected
  }
  
}
