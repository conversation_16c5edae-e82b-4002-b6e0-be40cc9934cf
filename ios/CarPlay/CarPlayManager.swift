//
//  TemplateManager.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON> on 30/03/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

import Foundation
import AVFoundation
import CarPlay
import os
import MediaPlayer
import <PERSON><PERSON>er

@available(iOS 14.0, *)
class CarPlayManager: NSObject {
  /// A reference to the CPInterfaceController that passes in after connecting to CarPlay.
  public var carplayInterfaceController: CPInterfaceController?
  /// The CarPlay session configuation contains information on restrictions for the specified interface.
  var sessionConfiguration: CPSessionConfiguration!
  
  // The observer of the playback state changes.
  var observers: [NSKeyValueObservation] = []
  var waitingPlayerObserver: NSKeyValueObservation?
  
  var recentTemplate : CPListTemplate!
  var libraryTemplate : CPListTemplate!
  var chaptersTemplate : CPListTemplate!
  
  var buttonPlaybackRate: CPNowPlayingImageButton!
  var buttonNext: CPNowPlayingImageButton!
  var buttonPrevious: CPNowPlayingImageButton!
  
  var closureItemPress: (() -> Void)?
  var tempLastState: String = RNCarPlayState.Connecting.rawValue
  
  /// Connects the root template to the CPInterfaceController.
  func connect(_ interfaceController: CPInterfaceController) {
    print("Connected to CarPlay window.")
    
    carplayInterfaceController = interfaceController
    carplayInterfaceController!.delegate = self
    sessionConfiguration = CPSessionConfiguration(delegate: self)
    CPNowPlayingTemplate.shared.add(self)
    self.recentTemplate = self.createBookListTemplate(title: "Gần đây", tabImage: "clock", playlist:SharedCarPlay.getRecentData())
    self.libraryTemplate = self.createBookListTemplate(title: "Thư viện", tabImage: "bookmark.circle", playlist:SharedCarPlay.getLibraryData())
    self.chaptersTemplate = TemplateManager.createChapterListTemplate()
    
    self.addObservers()
    
    let tabTemplates = CPTabBarTemplate(templates: [self.recentTemplate, self.libraryTemplate])
    
    self.carplayInterfaceController!.delegate = self
    self.carplayInterfaceController!.setRootTemplate(tabTemplates,  animated: true, completion: nil)
    SharedCarPlay.setInitRootTemplate(initRoot: true)
    let state = SharedCarPlay.getState()
    if (state == RNCarPlayState.Disconnected){
      let alertTemplate = CPAlertTemplate(titleVariants: ["Fonos đang khởi tạo dữ liệu cho CarPlay"], actions: [])
      self.carplayInterfaceController!.presentTemplate(alertTemplate, animated: true)
    }

  }
    /// Called when CarPlay disconnects.
  func disconnect() {
    print("Disconnected from CarPlay window.")
    for observersItem in self.observers {
      observersItem.invalidate()
    }
    self.observers.removeAll()
    self.waitingPlayerObserver?.invalidate()
    self.waitingPlayerObserver = nil
    self.carplayInterfaceController = nil
    SharedCarPlay.setInitRootTemplate(initRoot: false)
  }
}

@available(iOS 14.0, *)
extension CarPlayManager: CPInterfaceControllerDelegate {
  func templateWillAppear(_ aTemplate: CPTemplate, animated: Bool) {
    print("Template \(aTemplate.classForCoder) will appear.")
  }
  
  func templateDidAppear(_ aTemplate: CPTemplate, animated: Bool) {
    print("Template \(aTemplate.classForCoder) did appear.")
  }
  
  func templateWillDisappear(_ aTemplate: CPTemplate, animated: Bool) {
    print("Template \(aTemplate.classForCoder) will disappear.")
  }
  
  func templateDidDisappear(_ aTemplate: CPTemplate, animated: Bool) {
    print("Template \(aTemplate.classForCoder) did disappear.")
  }
}


// NOW PLAYING TEMPLATE SETTING GOING HEREEEEEEE
@available(iOS 14.0, *)
extension CarPlayManager: CPNowPlayingTemplateObserver {
  func nowPlayingTemplateUpNextButtonTapped(_ nowPlayingTemplate: CPNowPlayingTemplate) {
    // Show the queue of songs.
    self.chaptersTemplate = TemplateManager.createChapterListTemplate()
    if (SharedCarPlay.instance.isInitRootTemplate == true){
      self.carplayInterfaceController?.pushTemplate(self.chaptersTemplate, animated: true, completion: nil)
    }
  }
}

@available(iOS 14.0, *)
extension CarPlayManager: CPSessionConfigurationDelegate {
  func sessionConfiguration(_ sessionConfiguration: CPSessionConfiguration,
                            limitedUserInterfacesChanged limitedUserInterfaces: CPLimitableUserInterface) {
    print("Limited UI changed: \(limitedUserInterfaces)")
  }
}



@available(iOS 14.0, *)
extension CarPlayManager {
  func addObservers() {
    let rerentObserve = SharedCarPlay.instance.observe(\.recentData, options: .new) { sharedInstance, _change in
      if (!SharedCarPlay.instance.isWaitingInitPlayer){
        let section = self.generateBookListSection(playlist: SharedCarPlay.getRecentData())
        self.recentTemplate.updateSections([section])
      }
    }
    self.observers.append(rerentObserve)
    let libraryObserve = SharedCarPlay.instance.observe(\.libraryData, options: .new) { sharedInstance, _change in
      let section = self.generateBookListSection(playlist: SharedCarPlay.getLibraryData())
      self.libraryTemplate.updateSections([section])
    }
    self.observers.append(libraryObserve)
    let currentTrackObserve = SharedCarPlay.instance.observe(\.currentTrackIndex, options: .new) { sharedInstance, _change in
      let section = TemplateManager.generatChapterListSection()
      self.chaptersTemplate.updateSections([section])
      self.setupNowPlaying()
    }

    self.observers.append(currentTrackObserve)

    let stateObserve = SharedCarPlay.instance.observe(\.state, options: .new) { sharedInstance, _change in
      let state = SharedCarPlay.getState()
      if (self.tempLastState == state.rawValue){
        return
      } else {
        self.tempLastState = state.rawValue
      }
      switch(state) {
      case RNCarPlayState.Anonymous:
        self.carplayInterfaceController!.dismissTemplate(animated: true)
        let alertTemplate = CPAlertTemplate(titleVariants: ["Bạn cần đăng nhập trên điện thoại trước khi sử dụng Fonos trên CarPlay"], actions: [])
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
          // Sometime Template Don't display therefore this prevent that bug
          self.carplayInterfaceController!.presentTemplate(alertTemplate, animated: true)
        }
        break
      case RNCarPlayState.Connecting:
        self.carplayInterfaceController!.dismissTemplate(animated: true)
        let alertTemplate = CPAlertTemplate(titleVariants: ["Fonos đang khởi tạo dữ liệu cho CarPlay"], actions: [])
        self.carplayInterfaceController!.presentTemplate(alertTemplate, animated: true)
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
           // Excecute after 10 seconds. Set Time out for waiting
          self.carplayInterfaceController!.dismissTemplate(animated: true)
        }
        break
      case RNCarPlayState.ConnectedWithoutNetwork:
        self.carplayInterfaceController!.dismissTemplate(animated: true)
        if (!SharedCarPlay.instance.isPhoneConnected){
          let alertOke = CPAlertAction(title: "Đã hiểu" , style: .default, handler: { _ in
            self.carplayInterfaceController!.dismissTemplate(animated: true)
          })
          let alertTemplate = CPAlertTemplate(titleVariants: ["Đang không có kết nối mạng. Để hiển thị danh sách sách nói đã tải về, vui lòng mở ứng dụng Fonos trên điện thoại."], actions: [alertOke])
          self.carplayInterfaceController!.presentTemplate(alertTemplate, animated: true)
        } else {
          let alertOke = CPAlertAction(title: "Đã hiểu" , style: .default, handler: { _ in
            self.carplayInterfaceController!.dismissTemplate(animated: true)
          })
          let alertTemplate = CPAlertTemplate(titleVariants: ["Đang không có kết nối mạng. CarPlay chỉ hiện thị sách nói đã tải về điện thoại."], actions: [alertOke])
          self.carplayInterfaceController!.presentTemplate(alertTemplate, animated: true)
        }
        break
      case RNCarPlayState.Disconnected:
        self.carplayInterfaceController!.dismissTemplate(animated: true)
        break
      case RNCarPlayState.Connected:
        self.carplayInterfaceController!.dismissTemplate(animated: true)
        break
      }
    }
    self.observers.append(stateObserve)
    let playbackRateOberver = SharedCarPlay.instance.observe(\.playbackRate, options: .new) { sharedInstance, _change in
      self.setupNowPlaying()
    }
    self.observers.append(playbackRateOberver)
  }
  
  func generateBookListSection(playlist: [NSDictionary]) -> CPListSection {
    var listItems: [CPListItem] = []
    if (playlist.count > 0) {
      for playlistItem in playlist {
        let item = CPListItem(text: playlistItem["title"] as? String, detailText: playlistItem["author"] as? String)
        item.userInfo = playlistItem
        item.handler = {_, completion in
          if (!SharedCarPlay.instance.isWaitingInitPlayer){
            self.closureItemPress = completion
            RNCarPlay.emitEvent(withName: RNCarPlayEvent.Play, body:playlistItem["playData"])
            SharedCarPlay.setCurrentTrackIndex(value: -1)
            SharedCarPlay.setTracks(data: playlistItem["tracks"] as? [NSDictionary])
            SharedCarPlay.setIsWaitingInitPlayer(isWaiting: true)
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
               // Excecute after 10 seconds. Set Time out for waiting
              if (self.closureItemPress != nil){
                self.closureItemPress!()
                self.closureItemPress = nil
              }
              SharedCarPlay.setIsWaitingInitPlayer(isWaiting: false)
              self.waitingPlayerObserver?.invalidate()
              self.waitingPlayerObserver = nil
            }
            if (self.waitingPlayerObserver == nil){
              self.waitingPlayerObserver = SharedCarPlay.instance.observe(\.isWaitingInitPlayer, options: .new) { sharedInstance, _change in
                if (!sharedInstance.isWaitingInitPlayer){
                  completion()
                  self.closureItemPress = nil
                  self.waitingPlayerObserver?.invalidate()
                  self.waitingPlayerObserver = nil
                }
              }
            }
          } else {
            completion()
          }
        }
        if (playlistItem["artwork"] != nil) {
          if URL(string: playlistItem["artwork"] as! String) != nil {
            item.setImageUrl(URL(string: playlistItem["artwork"] as! String))
          }
        }
        
        listItems.append(item)
      }
    }
    var cpListTemp = CPListSection(items: listItems)
    if (listItems.count == 0){
      let item = CPListItem(text: "Không có dữ liệu", detailText: "Chưa có sách nói nào được thêm vào danh sách này")
      cpListTemp = CPListSection(items: [item])
    }
    return cpListTemp
  }
  
  private func setupNowPlaying() {
    let trackLength = SharedCarPlay.getTracks().count - 1
    CPNowPlayingTemplate.shared.isUpNextButtonEnabled = true
    CPNowPlayingTemplate.shared.upNextTitle = "Chương"
    self.buttonPlaybackRate = CPNowPlayingImageButton.init(image: UIImage(named:  self.getImageStringBaseOnPlaybackRate() as String)!, handler: { _ in
      self.requestUpdatePlaybackRate()
    })
    var buttonNextImage = UIImage(named: "forward_enable")!
    var buttonNextEnable = true
    if (SharedCarPlay.getCurrentTrackIndex() == trackLength) {
      buttonNextImage = UIImage(named: "forward")!
      buttonNextEnable = false
    }
    self.buttonNext = CPNowPlayingImageButton.init(image: buttonNextImage, handler: { _ in
      let trackLength = SharedCarPlay.getTracks().count - 1
      if (SharedCarPlay.getCurrentTrackIndex() < trackLength){
        let nextIndex = SharedCarPlay.getCurrentTrackIndex() + 1
        RNCarPlay.emitEvent(withName: RNCarPlayEvent.MoveToTrack, body:nextIndex)
        TemplateManager.setCurrectTrackIndexWithDebouce(index: nextIndex)
      }
    })
    self.buttonNext.isEnabled = buttonNextEnable
    self.buttonPrevious = CPNowPlayingImageButton.init(image: UIImage(named: "backward_enable")!, handler: { _ in
      RNCarPlay.emitEvent(withName: RNCarPlayEvent.MoveToPrevious, body: nil)
    })
    CPNowPlayingTemplate.shared.updateNowPlayingButtons([self.buttonPrevious, self.buttonPlaybackRate, self.buttonNext])
  }
  
  private func getImageStringBaseOnPlaybackRate() -> NSString {
    let currentPlaybackRate = SharedCarPlay.instance.playbackRate
    if (currentPlaybackRate <= 1.2){
      return "1x"
    } else if (currentPlaybackRate <= 1.7){
      return "1.5x"
    } else {
      return "2x"
    }
  }
  
  private func requestUpdatePlaybackRate() {
    let currentPlaybackRate = SharedCarPlay.instance.playbackRate
    var roundedPlaybackRate: Double! = 1.0
    if (currentPlaybackRate <= 1.2){
      roundedPlaybackRate = 1.5
    } else if (currentPlaybackRate <= 1.7){
      roundedPlaybackRate = 2.0
    } else {
      roundedPlaybackRate = 1.0
    }
    RNCarPlay.emitEvent(withName: RNCarPlayEvent.SetPlaybackRate, body:roundedPlaybackRate)
    SharedCarPlay.setPlaybackRate(value: roundedPlaybackRate)
  }
  
  func createBookListTemplate(title: String, tabImage: String, playlist: [NSDictionary])-> CPListTemplate {
    let section = self.generateBookListSection(playlist: playlist)
    let template = CPListTemplate(title: title, sections: [section])
    template.tabImage = UIImage(systemName: tabImage)
    return template
  }
  
  
}

@available(iOS 14.0, *)
extension CPListItem {
  
  convenience init(text: String?,
                   detailText: String?,
                   remoteImageUrl: URL?,
                   placeholder: UIImage?,
                   accessoryImage: UIImage? = nil,
                   accessoryType: CPListItemAccessoryType = .none) {
    self.init(text: text, detailText: detailText, image: placeholder, accessoryImage: accessoryImage, accessoryType: accessoryType)

    setImageUrl(remoteImageUrl)
  }
  
  func loadImage(from urlString: String, completion: @escaping (UIImage?) -> Void) {
      let url = URL(string: urlString)
      let resource = KF.ImageResource(downloadURL: url!, cacheKey: urlString)
      KingfisherManager.shared.retrieveImage(with: resource) { result in
          switch result {
          case .success(let value):
              completion(value.image)
          case .failure(let error):
              print("Error: \(error)")
              completion(nil)
          }
      }
   }

  func setImageUrl(_ url: URL?) {
    guard let imageUrl = url else { return }

    self.loadImage(from: imageUrl.absoluteString) { image in
      guard
        let carPlayImage = image?.carPlayImage
      else { return }
      DispatchQueue.main.async { [weak self] in
        self?.setImage(carPlayImage)
      }
    }
  }
}

@available(iOS 14.0, *)
extension UIImage {
  var carPlayImage: UIImage? {
    if (SharedCarPlay.instance.interfaceController != nil){
      let traits = SharedCarPlay.instance.interfaceController.carTraitCollection
      if (traits.displayScale > 0){
        let imageAsset = UIImageAsset()
        imageAsset.register(self, with: traits)
        return imageAsset.image(with: traits)
      }
    }
    return nil
  }
}
