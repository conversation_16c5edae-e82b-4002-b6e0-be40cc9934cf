//
//  Types.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 16/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

import Foundation


enum RNCarPlayState: String {
  case Anonymous, Connecting, Connected, ConnectedWithoutNetwork, Disconnected
}

enum RNCarPlayEvent: String {
  case PhoneAppOpen = "@carplay:phone:open"
  case DidConnect = "@carplay:connected"
  case DidDisConnect = "@carplay:disconnected"
  case Play = "@carplay:play"
  case MoveToTrack = "@carplay:move:to:track"
  case MoveToPrevious = "@carplay:move:to:previous"
  case SetPlaybackRate = "@carplay:set:playback:rate"
}
