//
//  TemplateManager.swift
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 21/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

import Foundation
import CarPlay
@available(iOS 14.0, *)
class TemplateManager: NSObject {

  
  static func createChapterListTemplate()-> CPListTemplate {
    let section = TemplateManager.generatChapterListSection()
    return CPListTemplate(title: "Chương", sections: [section])
  }
  
  static func generatChapterListSection()-> CPListSection {
    var listItems: [CPListItem] = []
    let tracks = SharedCarPlay.getTracks()
    let currentTrackIndex = SharedCarPlay.getCurrentTrackIndex()
    
    if tracks.count > 0 {
      for (index, playlistItem) in tracks.enumerated() {
        let item = CPListItem(text: playlistItem["title"] as? String, detailText: playlistItem["duration"] as? String)
        item.userInfo = playlistItem
        item.handler = {_, completion in
          RNCarPlay.emitEvent(withName: RNCarPlayEvent.MoveToTrack, body:index)
          setCurrectTrackIndexWithDebouce(index: index)
          completion()
        }
        item.isPlaying = index == currentTrackIndex
        item.playingIndicatorLocation = CPListItemPlayingIndicatorLocation.trailing
        listItems.append(item)
      }
      
    }
    return CPListSection(items: listItems)
  }
  
  static func setCurrectTrackIndexWithDebouce(index: NSInteger) -> Void {
    SharedCarPlay.setCurrentTrackIndex(value: index)
    SharedCarPlay.instance.shouldBlockUpdateFromJS = true
    DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
       // Excecute after 2 seconds
      SharedCarPlay.instance.shouldBlockUpdateFromJS = false
    }
  }
  
}
