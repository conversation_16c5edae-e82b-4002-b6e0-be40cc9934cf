//
//  RNCarPlay.m
//  FonosMobile
//
//  Created by <PERSON><PERSON><PERSON> on 14/04/2023.
//  Copyright © 2023 Facebook. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>

@interface RCT_EXTERN_MODULE(RNCarPlay, RCTEventEmitter)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

+ (BOOL)requiresMainQueueSetup
{
    return YES;
}

RCT_EXTERN_METHOD(setRecentData: (NSDictionaryArray *)data)
RCT_EXTERN_METHOD(setTracks: (NSDictionaryArray *)data)
RCT_EXTERN_METHOD(setLibraryData:(NSDictionaryArray *)data)
RCT_EXTERN_METHOD(setState: (NSString *)state)
RCT_EXTERN_METHOD(setCurrentTrackIndex:(NSInteger *)index)
RCT_EXTERN_METHOD(setPlaybackRate:(CGFloat *)index)
RCT_EXTERN_METHOD(checkConnection)
RCT_EXTERN_METHOD(navigateToNowPlaying)
@end
